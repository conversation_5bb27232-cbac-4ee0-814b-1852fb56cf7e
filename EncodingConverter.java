import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;

public class EncodingConverter {

    public static void main(String[] args) {
        String clientDir = "ruoyi-hgz/src/main/java/com/ruoyi/hgz/miitvipws/client";

        System.out.println("Starting to convert Java file encoding in client directory...");
        System.out.println("Target directory: " + clientDir);
        
        try {
            Path clientPath = Paths.get(clientDir);
            if (!Files.exists(clientPath)) {
                System.err.println("Error: Directory does not exist - " + clientDir);
                return;
            }

            // Get all Java files
            List<Path> javaFiles = Files.list(clientPath)
                .filter(path -> path.toString().endsWith(".java"))
                .collect(Collectors.toList());
            
            System.out.println("Found " + javaFiles.size() + " Java files");

            int successCount = 0;
            int failCount = 0;

            for (Path file : javaFiles) {
                try {
                    System.out.println("Processing: " + file.getFileName());

                    // Detect file encoding and read content
                    String content = readFileWithEncoding(file);

                    if (content != null) {
                        // Write file with UTF-8 encoding
                        Files.write(file, content.getBytes(StandardCharsets.UTF_8));
                        System.out.println("Success: " + file.getFileName());
                        successCount++;
                    } else {
                        System.out.println("Failed to read file: " + file.getFileName());
                        failCount++;
                    }

                } catch (Exception e) {
                    System.out.println("Conversion failed: " + file.getFileName() + " - " + e.getMessage());
                    failCount++;
                }
            }
            
            System.out.println();
            System.out.println("Encoding conversion completed!");
            System.out.println("Successfully converted: " + successCount + " files");
            System.out.println("Failed to convert: " + failCount + " files");

        } catch (Exception e) {
            System.err.println("Error during processing: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static String readFileWithEncoding(Path file) {
        // Try different encodings to read the file
        Charset[] encodings = {
            Charset.forName("GBK"),
            Charset.forName("GB2312"),
            StandardCharsets.UTF_8,
            Charset.defaultCharset()
        };
        
        for (Charset encoding : encodings) {
            try {
                byte[] bytes = Files.readAllBytes(file);
                String content = new String(bytes, encoding);
                
                // Simple check: if contains Chinese characters without garbled text, consider encoding correct
                if (isValidContent(content)) {
                    System.out.println("  Detected encoding: " + encoding.name());
                    return content;
                }
            } catch (Exception e) {
                // Continue trying next encoding
            }
        }

        // If all encodings fail, use default encoding
        try {
            byte[] bytes = Files.readAllBytes(file);
            return new String(bytes, Charset.defaultCharset());
        } catch (Exception e) {
            return null;
        }
    }
    
    private static boolean isValidContent(String content) {
        // Check if content contains obvious garbled text
        // This is a simple check, can be improved as needed
        return !content.contains("BOM") &&
               !content.contains("???") &&
               content.length() > 0;
    }
}
