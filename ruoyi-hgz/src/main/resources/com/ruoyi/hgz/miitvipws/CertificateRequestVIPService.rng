<?xml version="1.0" encoding="UTF-8"?>
<grammar xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns="http://relaxng.org/ns/structure/1.0" datatypeLibrary="http://www.w3.org/2001/XMLSchema-datatypes">
    <start>
        <element name="wsdl:definitions">
            <attribute name="name">
                <data type="NCName"/>
            </attribute>
            <attribute name="targetNamespace">
                <data type="anyURI"/>
            </attribute>
            <element name="wsdl:types">
                <element name="xsd:schema">
                    <attribute name="elementFormDefault">
                        <data type="NCName"/>
                    </attribute>
                    <attribute name="targetNamespace">
                        <data type="anyURI"/>
                    </attribute>
                    <attribute name="version">
                        <data type="decimal"/>
                    </attribute>
                    <oneOrMore>
                        <ref name="xsd.element"/>
                    </oneOrMore>
                    <oneOrMore>
                        <ref name="xsd.complexType"/>
                    </oneOrMore>
                </element>
            </element>
            <oneOrMore>
                <element name="wsdl:message">
                    <attribute name="name">
                        <data type="NCName"/>
                    </attribute>
                    <element name="wsdl:part">
                        <attribute name="element">
                            <data type="NMTOKEN"/>
                        </attribute>
                        <attribute name="name">
                            <data type="NCName"/>
                        </attribute>
                    </element>
                </element>
            </oneOrMore>
            <element name="wsdl:portType">
                <attribute name="name">
                    <data type="NCName"/>
                </attribute>
                <oneOrMore>
                    <ref name="wsdl.operation"/>
                </oneOrMore>
            </element>
            <element name="wsdl:binding">
                <attribute name="name"/>
                <attribute name="type">
                    <data type="NMTOKEN"/>
                </attribute>
                <element name="soap:binding">
                    <attribute name="style">
                        <data type="NCName"/>
                    </attribute>
                    <attribute name="transport">
                        <data type="anyURI"/>
                    </attribute>
                </element>
                <oneOrMore>
                    <ref name="wsdl.operation"/>
                </oneOrMore>
            </element>
            <element name="wsdl:service">
                <attribute name="name">
                    <data type="NCName"/>
                </attribute>
                <element name="wsdl:port">
                    <attribute name="binding">
                        <data type="anyURI"/>
                    </attribute>
                    <attribute name="name"/>
                    <element name="soap:address">
                        <attribute name="location">
                            <data type="anyURI"/>
                        </attribute>
                    </element>
                </element>
            </element>
        </element>
    </start>
    <define name="xsd.element">
        <element name="xsd:element">
            <optional>
                <attribute name="form">
                    <data type="NCName"/>
                </attribute>
            </optional>
            <optional>
                <attribute name="maxOccurs">
                    <data type="NCName"/>
                </attribute>
            </optional>
            <optional>
                <attribute name="minOccurs">
                    <data type="integer"/>
                </attribute>
            </optional>
            <attribute name="name">
                <data type="NCName"/>
            </attribute>
            <optional>
                <attribute name="nillable">
                    <data type="boolean"/>
                </attribute>
            </optional>
            <optional>
                <attribute name="type">
                    <data type="NMTOKEN"/>
                </attribute>
            </optional>
            <optional>
                <ref name="xsd.complexType"/>
            </optional>
        </element>
    </define>
    <define name="xsd.complexType">
        <element name="xsd:complexType">
            <optional>
                <attribute name="name">
                    <data type="NCName"/>
                </attribute>
            </optional>
            <zeroOrMore>
                <element name="xsd:sequence">
                    <zeroOrMore>
                        <ref name="xsd.element"/>
                    </zeroOrMore>
                </element>
            </zeroOrMore>
        </element>
    </define>
    <define name="wsdl.operation">
        <element name="wsdl:operation">
            <attribute name="name">
                <data type="NCName"/>
            </attribute>
            <optional>
                <element name="soap:operation">
                    <attribute name="soapAction">
                        <data type="anyURI"/>
                    </attribute>
                    <attribute name="style">
                        <data type="NCName"/>
                    </attribute>
                </element>
            </optional>
            <element name="wsdl:input">
                <optional>
                    <attribute name="message">
                        <data type="NMTOKEN"/>
                    </attribute>
                </optional>
                <attribute name="name">
                    <data type="NCName"/>
                </attribute>
                <optional>
                    <ref name="soap.body"/>
                </optional>
            </element>
            <element name="wsdl:output">
                <optional>
                    <attribute name="message">
                        <data type="NMTOKEN"/>
                    </attribute>
                </optional>
                <attribute name="name">
                    <data type="NCName"/>
                </attribute>
                <optional>
                    <ref name="soap.body"/>
                </optional>
            </element>
        </element>
    </define>
    <define name="soap.body">
        <element name="soap:body">
            <attribute name="use">
                <data type="NCName"/>
            </attribute>
        </element>
    </define>
</grammar>
