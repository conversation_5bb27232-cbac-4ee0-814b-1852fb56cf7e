<?xml version='1.0' encoding='UTF-8'?><wsdl:definitions xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:tns="http://www.vidc.info/certificate/operation/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:ns1="http://schemas.xmlsoap.org/soap/http" name="CertificateRequestVIP" targetNamespace="http://www.vidc.info/certificate/operation/">
  <wsdl:types>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://www.vidc.info/certificate/operation/" elementFormDefault="qualified" targetNamespace="http://www.vidc.info/certificate/operation/" version="1.0">

  <xs:element name="CertificateInfo" type="tns:CertificateInfo"/>

  <xs:element name="GetAfficheDP" type="tns:GetAfficheDP"/>

  <xs:element name="GetAfficheDPResponse" type="tns:GetAfficheDPResponse"/>

  <xs:element name="GetAfficheZC" type="tns:GetAfficheZC"/>

  <xs:element name="GetAfficheZCResponse" type="tns:GetAfficheZCResponse"/>

  <xs:element name="HelloWorld" type="tns:HelloWorld"/>

  <xs:element name="HelloWorldResponse" type="tns:HelloWorldResponse"/>

  <xs:element name="OperateResult" type="tns:OperateResult"/>

  <xs:element name="QueryByCondition" type="tns:QueryByCondition"/>

  <xs:element name="QueryByConditionResponse" type="tns:QueryByConditionResponse"/>

  <xs:element name="QueryCertificateSingle" type="tns:QueryCertificateSingle"/>

  <xs:element name="QueryCertificateSingleResponse" type="tns:QueryCertificateSingleResponse"/>

  <xs:element name="QueryHisByCondition" type="tns:QueryHisByCondition"/>

  <xs:element name="QueryHisByConditionResponse" type="tns:QueryHisByConditionResponse"/>

  <xs:element name="QueryOnWayByCondition" type="tns:QueryOnWayByCondition"/>

  <xs:element name="QueryOnWayByConditionResponse" type="tns:QueryOnWayByConditionResponse"/>

  <xs:element name="UploadDelete_Ent" type="tns:UploadDelete_Ent"/>

  <xs:element name="UploadDelete_EntResponse" type="tns:UploadDelete_EntResponse"/>

  <xs:element name="UploadInser_Ent" type="tns:UploadInser_Ent"/>

  <xs:element name="UploadInser_EntResponse" type="tns:UploadInser_EntResponse"/>

  <xs:element name="UploadOverTime_Ent" type="tns:UploadOverTime_Ent"/>

  <xs:element name="UploadOverTime_EntResponse" type="tns:UploadOverTime_EntResponse"/>

  <xs:element name="UploadUpdate_Ent" type="tns:UploadUpdate_Ent"/>

  <xs:element name="UploadUpdate_EntEX" type="tns:UploadUpdate_EntEX"/>

  <xs:element name="UploadUpdate_EntEXResponse" type="tns:UploadUpdate_EntEXResponse"/>

  <xs:element name="UploadUpdate_EntResponse" type="tns:UploadUpdate_EntResponse"/>

  <xs:complexType name="QueryCertificateSingle">
    <xs:sequence>
      <xs:element minOccurs="0" name="username" type="xs:string"/>
      <xs:element minOccurs="0" name="password" type="xs:string"/>
      <xs:element minOccurs="0" name="wzhgzbh" type="xs:string"/>
      <xs:element minOccurs="0" name="clsbdh" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="QueryCertificateSingleResponse">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="QueryCertificateSingleResult" type="tns:CertificateInfo"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="CertificateInfo">
    <xs:sequence>
      <xs:element form="unqualified" minOccurs="0" name="H_ID" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="CJH" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="CLSBDH" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="CLZZQYMC" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="CLLX" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="CLMC" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="CLPP" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="CLXH" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="CSYS" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="DPXH" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="FDJH" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="FDJXH" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="RLZL" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="PFBZ" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="PL" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="GL" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="ZXXS" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="QLJ" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="HLJ" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="LTS" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="LTGG" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="GBTHPS" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="ZJ" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="ZH" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="ZS" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="WKC" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="WKK" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="WKG" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="HXNBC" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="HXNBK" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="HXNBG" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="ZZL" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="EDZZL" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="ZBZL" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="ZZLLYXS" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="ZQYZZL" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="EDZK" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="BGCAZZDYXZZL" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="JSSZCRS" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="ZGCS" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="CLZZRQ" nillable="true" type="xs:dateTime"/>
      <xs:element form="unqualified" minOccurs="0" name="BZ" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="CPSCDZ" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="CZRQ" nillable="true" type="xs:dateTime"/>
      <xs:element form="unqualified" minOccurs="0" name="FZRQ" nillable="true" type="xs:dateTime"/>
      <xs:element form="unqualified" minOccurs="0" name="CLSCDWMC" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="YH" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="ZXZS" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="CDDBJ" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="PZXLH" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="CREATETIME" nillable="true" type="xs:dateTime"/>
      <xs:element form="unqualified" minOccurs="0" name="VEHICLE_STATUS" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="RESPONSE_CODE" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="CLIENT_HARDWARE_INFO" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="FEEDBACK_TIME" nillable="true" type="xs:dateTime"/>
      <xs:element form="unqualified" minOccurs="0" name="HD_HOST" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="HD_USER" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="UKEY" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="UPDATETIME" nillable="true" type="xs:dateTime"/>
      <xs:element form="unqualified" minOccurs="0" name="UPSEND_TAG" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="VERCODE" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="VERSION" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="CLZTXX" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="DYWYM" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="QYID" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="ZCHGZBH" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="ZZBH" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="CPH" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="DPHGZBH" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="DPID" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="GGSXRQ" nillable="true" type="xs:dateTime"/>
      <xs:element form="unqualified" minOccurs="0" name="HZDCZFS" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="HZDFS" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="PC" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="QYBZ" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="QYQTXX" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="QZDCZFS" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="QZDFS" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="WZHGZBH" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="JFPZID" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="VINBSYY" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="ISCXNF" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="ZYZYCMSBS" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="XNYQCJMSBS" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="HDMSBS" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="XNYQCZL" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="STQYMC" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="STSCDZ" nillable="true" type="xs:string"/>
      <xs:element form="unqualified" minOccurs="0" name="STSHXYDM" nillable="true" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="QueryHisByCondition">
    <xs:sequence>
      <xs:element minOccurs="0" name="username" type="xs:string"/>
      <xs:element minOccurs="0" name="password" type="xs:string"/>
      <xs:element minOccurs="0" name="wzhgzbh" type="xs:string"/>
      <xs:element minOccurs="0" name="clsbdh" type="xs:string"/>
      <xs:element minOccurs="0" name="startTime" type="xs:string"/>
      <xs:element minOccurs="0" name="endTime" type="xs:string"/>
      <xs:element minOccurs="0" name="applicType" type="xs:string"/>
      <xs:element name="status" type="xs:int"/>
      <xs:element name="pagesite" type="xs:int"/>
      <xs:element name="pageSize" type="xs:int"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="QueryHisByConditionResponse">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="QueryHisByConditionResult" type="tns:CertificateInfo"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="UploadOverTime_Ent">
    <xs:sequence>
      <xs:element minOccurs="0" name="username" type="xs:string"/>
      <xs:element minOccurs="0" name="password" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="certificateInfos" type="tns:CertificateInfo"/>
      <xs:element minOccurs="0" name="memo" type="xs:string"/>
      <xs:element minOccurs="0" name="ukey" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="UploadOverTime_EntResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="UploadOverTime_EntResult" type="tns:OperateResult"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="OperateResult">
    <xs:sequence>
      <xs:element name="ResultCode" type="xs:int"/>
      <xs:element minOccurs="0" name="ResultDetail">
        <xs:complexType>
          <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="NameValuePair" type="tns:NameValuePair"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="NameValuePair">
    <xs:sequence>
      <xs:element minOccurs="0" name="Name" type="xs:string"/>
      <xs:element minOccurs="0" name="Value" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="HelloWorld">
    <xs:sequence/>
  </xs:complexType>

  <xs:complexType name="HelloWorldResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="HelloWorldResult" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="UploadDelete_Ent">
    <xs:sequence>
      <xs:element minOccurs="0" name="username" type="xs:string"/>
      <xs:element minOccurs="0" name="password" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="certificateInfos" type="tns:CertificateInfo"/>
      <xs:element minOccurs="0" name="memo" type="xs:string"/>
      <xs:element minOccurs="0" name="ukey" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="UploadDelete_EntResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="UploadDelete_EntResult" type="tns:OperateResult"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="UploadUpdate_EntEX">
    <xs:sequence>
      <xs:element minOccurs="0" name="username" type="xs:string"/>
      <xs:element minOccurs="0" name="password" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="certificateInfos" type="tns:CertificateInfo"/>
      <xs:element minOccurs="0" name="memo" type="xs:string"/>
      <xs:element minOccurs="0" name="ukey" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="UploadUpdate_EntEXResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="UploadUpdate_EntEXResult" type="tns:OperateResult"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="GetAfficheZC">
    <xs:sequence>
      <xs:element minOccurs="0" name="username" type="xs:string"/>
      <xs:element minOccurs="0" name="password" type="xs:string"/>
      <xs:element minOccurs="0" name="pc" type="xs:string"/>
      <xs:element minOccurs="0" name="cph" type="xs:string"/>
      <xs:element minOccurs="0" name="clxh" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="GetAfficheZCResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="GetAfficheZCResult" type="xs:base64Binary"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="UploadInser_Ent">
    <xs:sequence>
      <xs:element minOccurs="0" name="username" type="xs:string"/>
      <xs:element minOccurs="0" name="password" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="certificateInfos" type="tns:CertificateInfo"/>
      <xs:element minOccurs="0" name="ukey" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="UploadInser_EntResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="UploadInser_EntResult" type="tns:OperateResult"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="GetAfficheDP">
    <xs:sequence>
      <xs:element minOccurs="0" name="username" type="xs:string"/>
      <xs:element minOccurs="0" name="password" type="xs:string"/>
      <xs:element minOccurs="0" name="pc" type="xs:string"/>
      <xs:element minOccurs="0" name="cph" type="xs:string"/>
      <xs:element minOccurs="0" name="dpxh" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="GetAfficheDPResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="GetAfficheDPResult" type="xs:base64Binary"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="QueryOnWayByCondition">
    <xs:sequence>
      <xs:element minOccurs="0" name="username" type="xs:string"/>
      <xs:element minOccurs="0" name="password" type="xs:string"/>
      <xs:element minOccurs="0" name="wzhgzbh" type="xs:string"/>
      <xs:element minOccurs="0" name="clsbdh" type="xs:string"/>
      <xs:element minOccurs="0" name="startTime" type="xs:string"/>
      <xs:element minOccurs="0" name="endTime" type="xs:string"/>
      <xs:element minOccurs="0" name="applicType" type="xs:string"/>
      <xs:element minOccurs="0" name="status" type="xs:string"/>
      <xs:element name="pagesite" type="xs:int"/>
      <xs:element name="pageSize" type="xs:int"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="QueryOnWayByConditionResponse">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="QueryOnWayByConditionResult" type="tns:CertificateInfo"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="QueryByCondition">
    <xs:sequence>
      <xs:element minOccurs="0" name="username" type="xs:string"/>
      <xs:element minOccurs="0" name="password" type="xs:string"/>
      <xs:element minOccurs="0" name="wzhgzbh" type="xs:string"/>
      <xs:element minOccurs="0" name="clsbdh" type="xs:string"/>
      <xs:element minOccurs="0" name="clxh" type="xs:string"/>
      <xs:element minOccurs="0" name="status" type="xs:string"/>
      <xs:element minOccurs="0" name="startTime" type="xs:string"/>
      <xs:element minOccurs="0" name="endTime" type="xs:string"/>
      <xs:element name="pagesite" type="xs:int"/>
      <xs:element name="pageSize" type="xs:int"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="QueryByConditionResponse">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="QueryByConditionResult" type="tns:CertificateInfo"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="UploadUpdate_Ent">
    <xs:sequence>
      <xs:element minOccurs="0" name="username" type="xs:string"/>
      <xs:element minOccurs="0" name="password" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="certificateInfos" type="tns:CertificateInfo"/>
      <xs:element minOccurs="0" name="memo" type="xs:string"/>
      <xs:element minOccurs="0" name="ukey" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="UploadUpdate_EntResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="UploadUpdate_EntResult" type="tns:OperateResult"/>
    </xs:sequence>
  </xs:complexType>

</xs:schema>
  </wsdl:types>
  <wsdl:message name="QueryCertificateSingle">
    <wsdl:part element="tns:QueryCertificateSingle" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="QueryHisByConditionResponse">
    <wsdl:part element="tns:QueryHisByConditionResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="UploadUpdate_EntEXResponse">
    <wsdl:part element="tns:UploadUpdate_EntEXResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="QueryHisByCondition">
    <wsdl:part element="tns:QueryHisByCondition" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="UploadOverTime_Ent">
    <wsdl:part element="tns:UploadOverTime_Ent" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="HelloWorld">
    <wsdl:part element="tns:HelloWorld" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="UploadDelete_Ent">
    <wsdl:part element="tns:UploadDelete_Ent" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="GetAfficheDPResponse">
    <wsdl:part element="tns:GetAfficheDPResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="QueryOnWayByConditionResponse">
    <wsdl:part element="tns:QueryOnWayByConditionResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="UploadUpdate_EntResponse">
    <wsdl:part element="tns:UploadUpdate_EntResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="QueryCertificateSingleResponse">
    <wsdl:part element="tns:QueryCertificateSingleResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="UploadDelete_EntResponse">
    <wsdl:part element="tns:UploadDelete_EntResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="UploadUpdate_EntEX">
    <wsdl:part element="tns:UploadUpdate_EntEX" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="GetAfficheZC">
    <wsdl:part element="tns:GetAfficheZC" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="UploadInser_Ent">
    <wsdl:part element="tns:UploadInser_Ent" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="GetAfficheDP">
    <wsdl:part element="tns:GetAfficheDP" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="QueryOnWayByCondition">
    <wsdl:part element="tns:QueryOnWayByCondition" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="QueryByCondition">
    <wsdl:part element="tns:QueryByCondition" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="GetAfficheZCResponse">
    <wsdl:part element="tns:GetAfficheZCResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="QueryByConditionResponse">
    <wsdl:part element="tns:QueryByConditionResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="HelloWorldResponse">
    <wsdl:part element="tns:HelloWorldResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="UploadUpdate_Ent">
    <wsdl:part element="tns:UploadUpdate_Ent" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="UploadOverTime_EntResponse">
    <wsdl:part element="tns:UploadOverTime_EntResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="UploadInser_EntResponse">
    <wsdl:part element="tns:UploadInser_EntResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:portType name="CertificateRequestVIP">
    <wsdl:operation name="QueryCertificateSingle">
      <wsdl:input message="tns:QueryCertificateSingle" name="QueryCertificateSingle">
    </wsdl:input>
      <wsdl:output message="tns:QueryCertificateSingleResponse" name="QueryCertificateSingleResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="QueryHisByCondition">
      <wsdl:input message="tns:QueryHisByCondition" name="QueryHisByCondition">
    </wsdl:input>
      <wsdl:output message="tns:QueryHisByConditionResponse" name="QueryHisByConditionResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UploadOverTime_Ent">
      <wsdl:input message="tns:UploadOverTime_Ent" name="UploadOverTime_Ent">
    </wsdl:input>
      <wsdl:output message="tns:UploadOverTime_EntResponse" name="UploadOverTime_EntResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="HelloWorld">
      <wsdl:input message="tns:HelloWorld" name="HelloWorld">
    </wsdl:input>
      <wsdl:output message="tns:HelloWorldResponse" name="HelloWorldResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UploadDelete_Ent">
      <wsdl:input message="tns:UploadDelete_Ent" name="UploadDelete_Ent">
    </wsdl:input>
      <wsdl:output message="tns:UploadDelete_EntResponse" name="UploadDelete_EntResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UploadUpdate_EntEX">
      <wsdl:input message="tns:UploadUpdate_EntEX" name="UploadUpdate_EntEX">
    </wsdl:input>
      <wsdl:output message="tns:UploadUpdate_EntEXResponse" name="UploadUpdate_EntEXResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAfficheZC">
      <wsdl:input message="tns:GetAfficheZC" name="GetAfficheZC">
    </wsdl:input>
      <wsdl:output message="tns:GetAfficheZCResponse" name="GetAfficheZCResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UploadInser_Ent">
      <wsdl:input message="tns:UploadInser_Ent" name="UploadInser_Ent">
    </wsdl:input>
      <wsdl:output message="tns:UploadInser_EntResponse" name="UploadInser_EntResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAfficheDP">
      <wsdl:input message="tns:GetAfficheDP" name="GetAfficheDP">
    </wsdl:input>
      <wsdl:output message="tns:GetAfficheDPResponse" name="GetAfficheDPResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="QueryOnWayByCondition">
      <wsdl:input message="tns:QueryOnWayByCondition" name="QueryOnWayByCondition">
    </wsdl:input>
      <wsdl:output message="tns:QueryOnWayByConditionResponse" name="QueryOnWayByConditionResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="QueryByCondition">
      <wsdl:input message="tns:QueryByCondition" name="QueryByCondition">
    </wsdl:input>
      <wsdl:output message="tns:QueryByConditionResponse" name="QueryByConditionResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UploadUpdate_Ent">
      <wsdl:input message="tns:UploadUpdate_Ent" name="UploadUpdate_Ent">
    </wsdl:input>
      <wsdl:output message="tns:UploadUpdate_EntResponse" name="UploadUpdate_EntResponse">
    </wsdl:output>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="CertificateRequestVIPSoapBinding" type="tns:CertificateRequestVIP">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="QueryCertificateSingle">
      <soap:operation soapAction="http://www.vidc.info/certificate/operation/QueryCertificateSingle" style="document"/>
      <wsdl:input name="QueryCertificateSingle">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="QueryCertificateSingleResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="QueryHisByCondition">
      <soap:operation soapAction="http://www.vidc.info/certificate/operation/QueryHisByCondition" style="document"/>
      <wsdl:input name="QueryHisByCondition">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="QueryHisByConditionResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UploadOverTime_Ent">
      <soap:operation soapAction="http://www.vidc.info/certificate/operation/UploadOverTime_Ent" style="document"/>
      <wsdl:input name="UploadOverTime_Ent">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="UploadOverTime_EntResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="HelloWorld">
      <soap:operation soapAction="http://www.vidc.info/certificate/operation/HelloWorld" style="document"/>
      <wsdl:input name="HelloWorld">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="HelloWorldResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UploadDelete_Ent">
      <soap:operation soapAction="http://www.vidc.info/certificate/operation/UploadDelete_Ent" style="document"/>
      <wsdl:input name="UploadDelete_Ent">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="UploadDelete_EntResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UploadUpdate_EntEX">
      <soap:operation soapAction="http://www.vidc.info/certificate/operation/UploadUpdate_EntEX" style="document"/>
      <wsdl:input name="UploadUpdate_EntEX">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="UploadUpdate_EntEXResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAfficheZC">
      <soap:operation soapAction="http://www.vidc.info/certificate/operation/GetAfficheZC" style="document"/>
      <wsdl:input name="GetAfficheZC">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="GetAfficheZCResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UploadInser_Ent">
      <soap:operation soapAction="http://www.vidc.info/certificate/operation/UploadInser_Ent" style="document"/>
      <wsdl:input name="UploadInser_Ent">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="UploadInser_EntResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAfficheDP">
      <soap:operation soapAction="http://www.vidc.info/certificate/operation/GetAfficheDP" style="document"/>
      <wsdl:input name="GetAfficheDP">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="GetAfficheDPResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="QueryOnWayByCondition">
      <soap:operation soapAction="http://www.vidc.info/certificate/operation/QueryOnWayByCondition" style="document"/>
      <wsdl:input name="QueryOnWayByCondition">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="QueryOnWayByConditionResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="QueryByCondition">
      <soap:operation soapAction="http://www.vidc.info/certificate/operation/QueryByCondition" style="document"/>
      <wsdl:input name="QueryByCondition">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="QueryByConditionResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UploadUpdate_Ent">
      <soap:operation soapAction="http://www.vidc.info/certificate/operation/UploadUpdate_Ent" style="document"/>
      <wsdl:input name="UploadUpdate_Ent">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="UploadUpdate_EntResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="CertificateRequestVIP">
    <wsdl:port binding="tns:CertificateRequestVIPSoapBinding" name="CertificateRequestVIPServiceImplPort">
      <soap:address location="http://hgz.miit.gov.cn/enterprise/services/CertificateRequestVIPService"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>