package com.ruoyi.hgz.utils;

import com.ruoyi.hgz.domain.WzHgzInfo;
import com.ruoyi.hgz.miitvipws.client.CertificateInfo;

import javax.xml.bind.JAXBElement;
import javax.xml.datatype.XMLGregorianCalendar;
import java.util.Calendar;

/**
 * 合格证信息转换工具类
 */
public class HgzConvertUtils {

    /**
     * 将CertificateInfo转换为WzHgzInfo
     */
    public static WzHgzInfo convertToWzHgzInfo(CertificateInfo info) {
        if (info == null) {
            return null;
        }

        WzHgzInfo wzHgzInfo = new WzHgzInfo();

        // 根据WzHgzInfo的字段定义进行转换
        wzHgzInfo.setH_ID(getElementValue(info.getHID()));
        wzHgzInfo.setCJH(getElementValue(info.getCJH()));
        wzHgzInfo.setCLSBDH(getElementValue(info.getCLSBDH()));
        wzHgzInfo.setCLZZQYMC(getElementValue(info.getCLZZQYMC()));
        wzHgzInfo.setCLLX(getElementValue(info.getCLLX()));
        wzHgzInfo.setCLMC(getElementValue(info.getCLMC()));
        wzHgzInfo.setCLPP(getElementValue(info.getCLPP()));
        wzHgzInfo.setCLXH(getElementValue(info.getCLXH()));
        wzHgzInfo.setCSYS(getElementValue(info.getCSYS()));
        wzHgzInfo.setDPXH(getElementValue(info.getDPXH()));
        wzHgzInfo.setFDJH(getElementValue(info.getFDJH()));
        wzHgzInfo.setFDJXH(getElementValue(info.getFDJXH()));
        wzHgzInfo.setRLZL(getElementValue(info.getRLZL()));
        wzHgzInfo.setPFBZ(getElementValue(info.getPFBZ()));
        wzHgzInfo.setPL(getElementValue(info.getPL()));
        wzHgzInfo.setGL(getElementValue(info.getGL()));
        wzHgzInfo.setZXXS(getElementValue(info.getZXXS()));
        wzHgzInfo.setQLJ(getElementValue(info.getQLJ()));
        wzHgzInfo.setHLJ(getElementValue(info.getHLJ()));
        wzHgzInfo.setLTS(getElementValue(info.getLTS()));
        wzHgzInfo.setLTGG(getElementValue(info.getLTGG()));
        wzHgzInfo.setGBTHPS(getElementValue(info.getGBTHPS()));
        wzHgzInfo.setZJ(getElementValue(info.getZJ()));
        wzHgzInfo.setZH(getElementValue(info.getZH()));
        wzHgzInfo.setZS(getElementValue(info.getZS()));
        wzHgzInfo.setWKC(getElementValue(info.getWKC()));
        wzHgzInfo.setWKK(getElementValue(info.getWKK()));
        wzHgzInfo.setWKG(getElementValue(info.getWKG()));
        wzHgzInfo.setHXNBC(getElementValue(info.getHXNBC()));
        wzHgzInfo.setHXNBK(getElementValue(info.getHXNBK()));
        wzHgzInfo.setHXNBG(getElementValue(info.getHXNBG()));
        wzHgzInfo.setZZL(getElementValue(info.getZZL()));
        wzHgzInfo.setEDZZL(getElementValue(info.getEDZZL()));
        wzHgzInfo.setZBZL(getElementValue(info.getZBZL()));
        wzHgzInfo.setZZLLYXS(getElementValue(info.getZZLLYXS()));
        wzHgzInfo.setZQYZZL(getElementValue(info.getZQYZZL()));
        wzHgzInfo.setEDZK(getElementValue(info.getEDZK()));
        wzHgzInfo.setBGCAZZDYXZZL(getElementValue(info.getBGCAZZDYXZZL()));
        wzHgzInfo.setJSSZCRS(getElementValue(info.getJSSZCRS()));
        wzHgzInfo.setZGCS(getElementValue(info.getZGCS()));
        wzHgzInfo.setCLZZRQ(getCalendarValue(info.getCLZZRQ()));
        wzHgzInfo.setBZ(getElementValue(info.getBZ()));
        wzHgzInfo.setCPSCDZ(getElementValue(info.getCPSCDZ()));
        wzHgzInfo.setCZRQ(getCalendarValue(info.getCZRQ()));
        wzHgzInfo.setFZRQ(getCalendarValue(info.getFZRQ()));
        wzHgzInfo.setCLSCDWMC(getElementValue(info.getCLSCDWMC()));
        wzHgzInfo.setYH(getElementValue(info.getYH()));
        wzHgzInfo.setZXZS(getElementValue(info.getZXZS()));
        wzHgzInfo.setCDDBJ(getElementValue(info.getCDDBJ()));
        wzHgzInfo.setPZXLH(getElementValue(info.getPZXLH()));
        wzHgzInfo.setCREATETIME(getCalendarValue(info.getCREATETIME()));
        wzHgzInfo.setVEHICLE_STATUS(getElementValue(info.getVEHICLESTATUS()));
        wzHgzInfo.setRESPONSE_CODE(getElementValue(info.getRESPONSECODE()));
        wzHgzInfo.setCLIENT_HARDWARE_INFO(getElementValue(info.getCLIENTHARDWAREINFO()));
        wzHgzInfo.setFEEDBACK_TIME(getCalendarValue(info.getFEEDBACKTIME()));
        wzHgzInfo.setHD_HOST(getElementValue(info.getHDHOST()));
        wzHgzInfo.setHD_USER(getElementValue(info.getHDUSER()));
        wzHgzInfo.setUKEY(getElementValue(info.getUKEY()));
        wzHgzInfo.setUPDATETIME(getCalendarValue(info.getUPDATETIME()));
        wzHgzInfo.setUPSEND_TAG(getElementValue(info.getUPSENDTAG()));
        wzHgzInfo.setVERCODE(getElementValue(info.getVERCODE()));
        wzHgzInfo.setVERSION(getElementValue(info.getVERSION()));
        wzHgzInfo.setCLZTXX(getElementValue(info.getCLZTXX()));
        wzHgzInfo.setDYWYM(getElementValue(info.getDYWYM()));
        wzHgzInfo.setQYID(getElementValue(info.getQYID()));
        wzHgzInfo.setZCHGZBH(getElementValue(info.getZCHGZBH()));
        wzHgzInfo.setZZBH(getElementValue(info.getZZBH()));
        wzHgzInfo.setCPH(getElementValue(info.getCPH()));
        wzHgzInfo.setDPHGZBH(getElementValue(info.getDPHGZBH()));
        wzHgzInfo.setDPID(getElementValue(info.getDPID()));
        wzHgzInfo.setGGSXRQ(getCalendarValue(info.getGGSXRQ()));
        wzHgzInfo.setHZDCZFS(getElementValue(info.getHZDCZFS()));
        wzHgzInfo.setHZDFS(getElementValue(info.getHZDFS()));
        wzHgzInfo.setPC(getElementValue(info.getPC()));
        wzHgzInfo.setQYBZ(getElementValue(info.getQYBZ()));
        wzHgzInfo.setQYQTXX(getElementValue(info.getQYQTXX()));
        wzHgzInfo.setQZDCZFS(getElementValue(info.getQZDCZFS()));
        wzHgzInfo.setQZDFS(getElementValue(info.getQZDFS()));
        wzHgzInfo.setWZHGZBH(getElementValue(info.getWZHGZBH()));
        wzHgzInfo.setJFPZID(getElementValue(info.getJFPZID()));
        wzHgzInfo.setVINBSYY(getElementValue(info.getVINBSYY()));
        wzHgzInfo.setISCXNF(getElementValue(info.getISCXNF()));
        wzHgzInfo.setZYZYCMSBS(getElementValue(info.getZYZYCMSBS()));
        wzHgzInfo.setXNYQCJMSBS(getElementValue(info.getXNYQCJMSBS()));
        wzHgzInfo.setHDMSBS(getElementValue(info.getHDMSBS()));
        wzHgzInfo.setXNYQCZL(getElementValue(info.getXNYQCZL()));

        return wzHgzInfo;
    }

    /**
     * 安全获取JAXBElement中的String值
     */
    private static String getElementValue(JAXBElement<String> element) {
        return element != null ? element.getValue() : null;
    }

    /**
     * 安全获取JAXBElement中的Calendar值
     */
    private static Calendar getCalendarValue(JAXBElement<XMLGregorianCalendar> element) {
        if (element != null && element.getValue() != null) {
            return element.getValue().toGregorianCalendar();
        }
        return null;
    }
}
