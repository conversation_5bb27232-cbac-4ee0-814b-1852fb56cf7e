
package com.ruoyi.hgz.miitvipws.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>GetAfficheZCResponse complex type鐨� Java 绫汇��
 * 
 * <p>浠ヤ笅妯″紡鐗囨鎸囧畾鍖呭惈鍦ㄦ绫讳腑鐨勯鏈熷唴瀹广��
 * 
 * <pre>
 * &lt;complexType name="GetAfficheZCResponse">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="GetAfficheZCResult" type="{http://www.w3.org/2001/XMLSchema}base64Binary" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "GetAfficheZCResponse", propOrder = {
    "getAfficheZCResult"
})
public class GetAfficheZCResponse {

    @XmlElement(name = "GetAfficheZCResult", namespace = "http://www.vidc.info/certificate/operation/")
    protected byte[] getAfficheZCResult;

    /**
     * 鑾峰彇getAfficheZCResult灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     byte[]
     */
    public byte[] getGetAfficheZCResult() {
        return getAfficheZCResult;
    }

    /**
     * 璁剧疆getAfficheZCResult灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     byte[]
     */
    public void setGetAfficheZCResult(byte[] value) {
        this.getAfficheZCResult = value;
    }

}
