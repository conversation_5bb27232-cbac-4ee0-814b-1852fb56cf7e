
package com.ruoyi.hgz.miitvipws.client;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>QueryByConditionResponse complex type鐨� Java 绫汇��
 * 
 * <p>浠ヤ笅妯″紡鐗囨鎸囧畾鍖呭惈鍦ㄦ绫讳腑鐨勯鏈熷唴瀹广��
 * 
 * <pre>
 * &lt;complexType name="QueryByConditionResponse">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="QueryByConditionResult" type="{http://www.vidc.info/certificate/operation/}CertificateInfo" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "QueryByConditionResponse", propOrder = {
    "queryByConditionResult"
})
public class QueryByConditionResponse {

    @XmlElement(name = "QueryByConditionResult", namespace = "http://www.vidc.info/certificate/operation/")
    protected List<CertificateInfo> queryByConditionResult;

    /**
     * Gets the value of the queryByConditionResult property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the queryByConditionResult property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getQueryByConditionResult().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link CertificateInfo }
     * 
     * 
     */
    public List<CertificateInfo> getQueryByConditionResult() {
        if (queryByConditionResult == null) {
            queryByConditionResult = new ArrayList<CertificateInfo>();
        }
        return this.queryByConditionResult;
    }

}
