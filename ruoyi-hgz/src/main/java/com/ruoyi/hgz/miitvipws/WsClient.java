package com.ruoyi.hgz.miitvipws;

import com.ruoyi.hgz.miitvipws.client.CertificateInfo;
import java.util.List;

/**
 * Unified WebService Client
 * Provides a simple API interface by wrapping SmartWSClient
 * Uses multiple strategies for maximum reliability
 *
 * <AUTHOR>
 */
public class WsClient {

    /**
     * Test connection
     *
     * @return Server response message
     * @throws RuntimeException when connection fails
     */
    public static String testConnection() {
        return SmartWSClient.helloWorld();
    }

    /**
     * Query single certificate information
     *
     * @param username Username
     * @param password Password
     * @param wzhgzbh Certificate number (can be empty)
     * @param clsbdh Vehicle identification number
     * @return Certificate information list
     * @throws RuntimeException when query fails
     */
    public static List<CertificateInfo> queryCertificateSingle(String username, String password,
                                                               String wzhgzbh, String clsbdh) {
        return SmartWSClient.queryCertificateSingle(username, password, wzhgzbh, clsbdh);
    }

    /**
     * Query certificate information by conditions
     * Note: Currently delegates to queryCertificateSingle as the underlying clients
     * may not support full conditional queries
     *
     * @param username Username
     * @param password Password
     * @param wzhgzbh Certificate number
     * @param clsbdh Vehicle identification number
     * @param clxh Vehicle model
     * @param status Status
     * @param startTime Start time
     * @param endTime End time
     * @param pagesite Page number
     * @param pageSize Page size
     * @return Certificate information list
     * @throws RuntimeException when query fails
     */
    public static List<CertificateInfo> queryByCondition(String username, String password,
                                                         String wzhgzbh, String clsbdh, String clxh,
                                                         String status, String startTime, String endTime,
                                                         int pagesite, int pageSize) {
        // For now, delegate to single query as most underlying clients don't support full conditional queries
        return SmartWSClient.queryCertificateSingle(username, password, wzhgzbh, clsbdh);
    }

    /**
     * Reset connection
     * Clear cached port instance and force reconnection
     */
    public static synchronized void resetConnection() {
        SmartWSClient.resetConnection();
    }

    /**
     * Validate connection
     * @return true if connection is normal, false if connection is abnormal
     */
    public static boolean validateConnection() {
        return SmartWSClient.validateConnection();
    }

    /**
     * Get service information
     * @return Service information string
     */
    public static String getServiceInfo() {
        return SmartWSClient.getServiceInfo();
    }

    /**
     * Get connection status
     * Note: SmartWSClient doesn't have isConnected method, so we validate instead
     * @return true if connection is valid, false otherwise
     */
    public static boolean isConnected() {
        return validateConnection();
    }
}
