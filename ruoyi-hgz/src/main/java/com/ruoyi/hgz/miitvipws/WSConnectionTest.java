package com.ruoyi.hgz.miitvipws;

import com.ruoyi.hgz.miitvipws.client.CertificateInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.List;

/**
 * WebService连接测试类
 */
public class WSConnectionTest {
    
    private static final Logger logger = LoggerFactory.getLogger(WSConnectionTest.class);
    
    public static void main(String[] args) {
        System.out.println("=== WebService连接测试开始 ===");
        System.out.println("测试时间: " + new Date());
        
        // 测试1: 连接测试
        testConnection();
        
        // 测试2: 查询测试（如果连接成功）
        testQuery();
        
        System.out.println("=== WebService连接测试结束 ===");
    }
    
    /**
     * 测试连接
     */
    private static void testConnection() {
        System.out.println("\n--- 测试1: 连接测试 ---");
        
        try {
            String result = ImprovedWSClient.testConnection();
            System.out.println("✓ 连接测试成功");
            System.out.println("服务器响应: " + result);
        } catch (Exception e) {
            System.out.println("✗ 连接测试失败");
            System.out.println("错误信息: " + e.getMessage());
            logger.error("连接测试失败", e);
        }
    }
    
    /**
     * 测试查询
     */
    private static void testQuery() {
        System.out.println("\n--- 测试2: 查询测试 ---");
        
        try {
            // 使用测试参数进行查询
            String username = "HX231008U001";
            String password = "D#$>sy38";
            String wzhgzbh = "";
            String clsbdh = "LGHY5J2G2RC053149";
            
            System.out.println("查询参数:");
            System.out.println("  用户名: " + username);
            System.out.println("  密码: " + password);
            System.out.println("  网证合格证编号: " + wzhgzbh);
            System.out.println("  车辆识别代号: " + clsbdh);
            
            List<CertificateInfo> results = ImprovedWSClient.queryCertificateSingle(
                username, password, wzhgzbh, clsbdh);
            
            if (results != null && !results.isEmpty()) {
                System.out.println("✓ 查询成功");
                System.out.println("返回记录数: " + results.size());
                
                // 显示第一条记录的详细信息
                CertificateInfo firstRecord = results.get(0);
                System.out.println("第一条记录信息:");
                if (firstRecord.getWZHGZBH() != null && firstRecord.getWZHGZBH().getValue() != null) {
                    System.out.println("  网证合格证编号: " + firstRecord.getWZHGZBH().getValue());
                }
                if (firstRecord.getCLSBDH() != null && firstRecord.getCLSBDH().getValue() != null) {
                    System.out.println("  车辆识别代号: " + firstRecord.getCLSBDH().getValue());
                }
            } else {
                System.out.println("✓ 查询成功，但未找到匹配记录");
            }
            
        } catch (Exception e) {
            System.out.println("✗ 查询测试失败");
            System.out.println("错误信息: " + e.getMessage());
            logger.error("查询测试失败", e);
        }
    }
    
    /**
     * 测试不同的连接方式
     */
    public static void testDifferentConnectionMethods() {
        System.out.println("\n--- 测试不同连接方式 ---");
        
        // 测试WSDL方式
        System.out.println("1. 测试WSDL方式连接:");
        try {
            String result = ImprovedWSClient.getPortWithWSDL().helloWorld();
            System.out.println("✓ WSDL方式连接成功: " + result);
        } catch (Exception e) {
            System.out.println("✗ WSDL方式连接失败: " + e.getMessage());
        }
        
        // 测试动态方式
        System.out.println("\n2. 测试动态方式连接:");
        try {
            String result = ImprovedWSClient.getPortWithoutWSDL().helloWorld();
            System.out.println("✓ 动态方式连接成功: " + result);
        } catch (Exception e) {
            System.out.println("✗ 动态方式连接失败: " + e.getMessage());
        }
    }
    
    /**
     * 性能测试
     */
    public static void performanceTest() {
        System.out.println("\n--- 性能测试 ---");
        
        int testCount = 5;
        long totalTime = 0;
        int successCount = 0;
        
        for (int i = 1; i <= testCount; i++) {
            System.out.println("第 " + i + " 次测试:");
            long startTime = System.currentTimeMillis();
            
            try {
                String result = ImprovedWSClient.testConnection();
                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;
                totalTime += duration;
                successCount++;
                
                System.out.println("  ✓ 成功 - 耗时: " + duration + "ms");
            } catch (Exception e) {
                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;
                System.out.println("  ✗ 失败 - 耗时: " + duration + "ms - " + e.getMessage());
            }
        }
        
        System.out.println("\n性能测试结果:");
        System.out.println("  总测试次数: " + testCount);
        System.out.println("  成功次数: " + successCount);
        System.out.println("  成功率: " + (successCount * 100.0 / testCount) + "%");
        if (successCount > 0) {
            System.out.println("  平均响应时间: " + (totalTime / successCount) + "ms");
        }
    }
}
