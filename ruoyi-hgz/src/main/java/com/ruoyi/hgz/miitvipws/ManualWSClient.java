package com.ruoyi.hgz.miitvipws;

import com.ruoyi.hgz.miitvipws.client.CertificateInfo;
import com.ruoyi.hgz.miitvipws.client.CertificateRequestVIP;

import javax.xml.namespace.QName;
import javax.xml.ws.BindingProvider;
import javax.xml.ws.Service;
import java.net.URL;
import java.util.List;

public class ManualWSClient {
    private static final String NAMESPACE_URI = "http://www.vidc.info/certificate/operation/";
    private static final String SERVICE_NAME = "CertificateRequestVIP";
    private static final String PORT_NAME = "CertificateRequestVIPServiceImplPort";
    private static final String SERVICE_URL = "https://hgz.miit.gov.cn/enterprise/services/CertificateRequestVIPService";
    
    private static CertificateRequestVIP port;
    
    static {
        try {
            WSClient.initSSL(); // 复用SSL初始化
        } catch (Exception e) {
            throw new RuntimeException("初始化SSL失败", e);
        }
    }
    
    public static synchronized CertificateRequestVIP getPort() {
        if (port == null) {
            try {
                // 创建服务和端口，不使用WSDL
                QName serviceQName = new QName(NAMESPACE_URI, SERVICE_NAME);
                QName portQName = new QName(NAMESPACE_URI, PORT_NAME);
                
                Service service = Service.create(serviceQName);
                service.addPort(portQName, 
                                javax.xml.ws.soap.SOAPBinding.SOAP11HTTP_BINDING, 
                                SERVICE_URL);
                
                port = service.getPort(portQName, CertificateRequestVIP.class);
                
                // 设置端点地址
                BindingProvider bp = (BindingProvider) port;
                bp.getRequestContext().put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, SERVICE_URL);
                
                // 设置超时
                bp.getRequestContext().put("com.sun.xml.ws.connect.timeout", 30000);
                bp.getRequestContext().put("com.sun.xml.ws.request.timeout", 60000);
                
            } catch (Exception e) {
                throw new RuntimeException("创建WebService客户端失败", e);
            }
        }
        return port;
    }
    
    // 测试方法
    public static String testHelloWorld() {
        try {
            return getPort().helloWorld();
        } catch (Exception e) {
            e.printStackTrace();
            return "Error: " + e.getMessage();
        }
    }
    
    // 查询方法
    public static List<CertificateInfo> queryCertificateSingle(String username, String password, String wzhgzbh, String clsbdh) {
        try {
            return getPort().queryCertificateSingle(username, password, wzhgzbh, clsbdh);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("查询失败: " + e.getMessage(), e);
        }
    }
}