
package com.ruoyi.hgz.miitvipws.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>GetAfficheDP complex type鐨� Java 绫汇��
 * 
 * <p>浠ヤ笅妯″紡鐗囨鎸囧畾鍖呭惈鍦ㄦ绫讳腑鐨勯鏈熷唴瀹广��
 * 
 * <pre>
 * &lt;complexType name="GetAfficheDP">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="username" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="password" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pc" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="cph" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="dpxh" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "GetAfficheDP", propOrder = {
    "username",
    "password",
    "pc",
    "cph",
    "dpxh"
})
public class GetAfficheDP {

    @XmlElement(namespace = "http://www.vidc.info/certificate/operation/")
    protected String username;
    @XmlElement(namespace = "http://www.vidc.info/certificate/operation/")
    protected String password;
    @XmlElement(namespace = "http://www.vidc.info/certificate/operation/")
    protected String pc;
    @XmlElement(namespace = "http://www.vidc.info/certificate/operation/")
    protected String cph;
    @XmlElement(namespace = "http://www.vidc.info/certificate/operation/")
    protected String dpxh;

    /**
     * 鑾峰彇username灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUsername() {
        return username;
    }

    /**
     * 璁剧疆username灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUsername(String value) {
        this.username = value;
    }

    /**
     * 鑾峰彇password灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPassword() {
        return password;
    }

    /**
     * 璁剧疆password灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPassword(String value) {
        this.password = value;
    }

    /**
     * 鑾峰彇pc灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPc() {
        return pc;
    }

    /**
     * 璁剧疆pc灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPc(String value) {
        this.pc = value;
    }

    /**
     * 鑾峰彇cph灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCph() {
        return cph;
    }

    /**
     * 璁剧疆cph灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCph(String value) {
        this.cph = value;
    }

    /**
     * 鑾峰彇dpxh灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDpxh() {
        return dpxh;
    }

    /**
     * 璁剧疆dpxh灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDpxh(String value) {
        this.dpxh = value;
    }

}
