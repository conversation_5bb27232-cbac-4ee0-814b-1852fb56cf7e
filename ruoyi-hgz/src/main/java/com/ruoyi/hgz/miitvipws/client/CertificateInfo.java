
package com.ruoyi.hgz.miitvipws.client;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>CertificateInfo complex type鐨� Java 绫汇��
 * 
 * <p>浠ヤ笅妯″紡鐗囨鎸囧畾鍖呭惈鍦ㄦ绫讳腑鐨勯鏈熷唴瀹广��
 * 
 * <pre>
 * &lt;complexType name="CertificateInfo">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="H_ID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="CJH" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="CLSBDH" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="CLZZQYMC" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="CLLX" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="CLMC" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="CLPP" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="CLXH" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="CSYS" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="DPXH" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="FDJH" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="FDJXH" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="RLZL" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="PFBZ" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="PL" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="GL" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="ZXXS" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="QLJ" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="HLJ" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="LTS" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="LTGG" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="GBTHPS" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="ZJ" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="ZH" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="ZS" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="WKC" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="WKK" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="WKG" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="HXNBC" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="HXNBK" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="HXNBG" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="ZZL" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="EDZZL" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="ZBZL" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="ZZLLYXS" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="ZQYZZL" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="EDZK" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="BGCAZZDYXZZL" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="JSSZCRS" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="ZGCS" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="CLZZRQ" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0" form="unqualified"/>
 *         &lt;element name="BZ" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="CPSCDZ" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="CZRQ" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0" form="unqualified"/>
 *         &lt;element name="FZRQ" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0" form="unqualified"/>
 *         &lt;element name="CLSCDWMC" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="YH" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="ZXZS" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="CDDBJ" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="PZXLH" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="CREATETIME" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0" form="unqualified"/>
 *         &lt;element name="VEHICLE_STATUS" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="RESPONSE_CODE" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="CLIENT_HARDWARE_INFO" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="FEEDBACK_TIME" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0" form="unqualified"/>
 *         &lt;element name="HD_HOST" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="HD_USER" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="UKEY" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="UPDATETIME" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0" form="unqualified"/>
 *         &lt;element name="UPSEND_TAG" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="VERCODE" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="VERSION" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="CLZTXX" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="DYWYM" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="QYID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="ZCHGZBH" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="ZZBH" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="CPH" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="DPHGZBH" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="DPID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="GGSXRQ" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0" form="unqualified"/>
 *         &lt;element name="HZDCZFS" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="HZDFS" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="PC" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="QYBZ" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="QYQTXX" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="QZDCZFS" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="QZDFS" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="WZHGZBH" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="JFPZID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="VINBSYY" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="ISCXNF" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="ZYZYCMSBS" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="XNYQCJMSBS" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="HDMSBS" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="XNYQCZL" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="STQYMC" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="STSCDZ" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *         &lt;element name="STSHXYDM" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0" form="unqualified"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CertificateInfo", propOrder = {
    "hid",
    "cjh",
    "clsbdh",
    "clzzqymc",
    "cllx",
    "clmc",
    "clpp",
    "clxh",
    "csys",
    "dpxh",
    "fdjh",
    "fdjxh",
    "rlzl",
    "pfbz",
    "pl",
    "gl",
    "zxxs",
    "qlj",
    "hlj",
    "lts",
    "ltgg",
    "gbthps",
    "zj",
    "zh",
    "zs",
    "wkc",
    "wkk",
    "wkg",
    "hxnbc",
    "hxnbk",
    "hxnbg",
    "zzl",
    "edzzl",
    "zbzl",
    "zzllyxs",
    "zqyzzl",
    "edzk",
    "bgcazzdyxzzl",
    "jsszcrs",
    "zgcs",
    "clzzrq",
    "bz",
    "cpscdz",
    "czrq",
    "fzrq",
    "clscdwmc",
    "yh",
    "zxzs",
    "cddbj",
    "pzxlh",
    "createtime",
    "vehiclestatus",
    "responsecode",
    "clienthardwareinfo",
    "feedbacktime",
    "hdhost",
    "hduser",
    "ukey",
    "updatetime",
    "upsendtag",
    "vercode",
    "version",
    "clztxx",
    "dywym",
    "qyid",
    "zchgzbh",
    "zzbh",
    "cph",
    "dphgzbh",
    "dpid",
    "ggsxrq",
    "hzdczfs",
    "hzdfs",
    "pc",
    "qybz",
    "qyqtxx",
    "qzdczfs",
    "qzdfs",
    "wzhgzbh",
    "jfpzid",
    "vinbsyy",
    "iscxnf",
    "zyzycmsbs",
    "xnyqcjmsbs",
    "hdmsbs",
    "xnyqczl",
    "stqymc",
    "stscdz",
    "stshxydm"
})
public class CertificateInfo {

    @XmlElementRef(name = "H_ID", type = JAXBElement.class, required = false)
    protected JAXBElement<String> hid;
    @XmlElementRef(name = "CJH", type = JAXBElement.class, required = false)
    protected JAXBElement<String> cjh;
    @XmlElementRef(name = "CLSBDH", type = JAXBElement.class, required = false)
    protected JAXBElement<String> clsbdh;
    @XmlElementRef(name = "CLZZQYMC", type = JAXBElement.class, required = false)
    protected JAXBElement<String> clzzqymc;
    @XmlElementRef(name = "CLLX", type = JAXBElement.class, required = false)
    protected JAXBElement<String> cllx;
    @XmlElementRef(name = "CLMC", type = JAXBElement.class, required = false)
    protected JAXBElement<String> clmc;
    @XmlElementRef(name = "CLPP", type = JAXBElement.class, required = false)
    protected JAXBElement<String> clpp;
    @XmlElementRef(name = "CLXH", type = JAXBElement.class, required = false)
    protected JAXBElement<String> clxh;
    @XmlElementRef(name = "CSYS", type = JAXBElement.class, required = false)
    protected JAXBElement<String> csys;
    @XmlElementRef(name = "DPXH", type = JAXBElement.class, required = false)
    protected JAXBElement<String> dpxh;
    @XmlElementRef(name = "FDJH", type = JAXBElement.class, required = false)
    protected JAXBElement<String> fdjh;
    @XmlElementRef(name = "FDJXH", type = JAXBElement.class, required = false)
    protected JAXBElement<String> fdjxh;
    @XmlElementRef(name = "RLZL", type = JAXBElement.class, required = false)
    protected JAXBElement<String> rlzl;
    @XmlElementRef(name = "PFBZ", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pfbz;
    @XmlElementRef(name = "PL", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pl;
    @XmlElementRef(name = "GL", type = JAXBElement.class, required = false)
    protected JAXBElement<String> gl;
    @XmlElementRef(name = "ZXXS", type = JAXBElement.class, required = false)
    protected JAXBElement<String> zxxs;
    @XmlElementRef(name = "QLJ", type = JAXBElement.class, required = false)
    protected JAXBElement<String> qlj;
    @XmlElementRef(name = "HLJ", type = JAXBElement.class, required = false)
    protected JAXBElement<String> hlj;
    @XmlElementRef(name = "LTS", type = JAXBElement.class, required = false)
    protected JAXBElement<String> lts;
    @XmlElementRef(name = "LTGG", type = JAXBElement.class, required = false)
    protected JAXBElement<String> ltgg;
    @XmlElementRef(name = "GBTHPS", type = JAXBElement.class, required = false)
    protected JAXBElement<String> gbthps;
    @XmlElementRef(name = "ZJ", type = JAXBElement.class, required = false)
    protected JAXBElement<String> zj;
    @XmlElementRef(name = "ZH", type = JAXBElement.class, required = false)
    protected JAXBElement<String> zh;
    @XmlElementRef(name = "ZS", type = JAXBElement.class, required = false)
    protected JAXBElement<String> zs;
    @XmlElementRef(name = "WKC", type = JAXBElement.class, required = false)
    protected JAXBElement<String> wkc;
    @XmlElementRef(name = "WKK", type = JAXBElement.class, required = false)
    protected JAXBElement<String> wkk;
    @XmlElementRef(name = "WKG", type = JAXBElement.class, required = false)
    protected JAXBElement<String> wkg;
    @XmlElementRef(name = "HXNBC", type = JAXBElement.class, required = false)
    protected JAXBElement<String> hxnbc;
    @XmlElementRef(name = "HXNBK", type = JAXBElement.class, required = false)
    protected JAXBElement<String> hxnbk;
    @XmlElementRef(name = "HXNBG", type = JAXBElement.class, required = false)
    protected JAXBElement<String> hxnbg;
    @XmlElementRef(name = "ZZL", type = JAXBElement.class, required = false)
    protected JAXBElement<String> zzl;
    @XmlElementRef(name = "EDZZL", type = JAXBElement.class, required = false)
    protected JAXBElement<String> edzzl;
    @XmlElementRef(name = "ZBZL", type = JAXBElement.class, required = false)
    protected JAXBElement<String> zbzl;
    @XmlElementRef(name = "ZZLLYXS", type = JAXBElement.class, required = false)
    protected JAXBElement<String> zzllyxs;
    @XmlElementRef(name = "ZQYZZL", type = JAXBElement.class, required = false)
    protected JAXBElement<String> zqyzzl;
    @XmlElementRef(name = "EDZK", type = JAXBElement.class, required = false)
    protected JAXBElement<String> edzk;
    @XmlElementRef(name = "BGCAZZDYXZZL", type = JAXBElement.class, required = false)
    protected JAXBElement<String> bgcazzdyxzzl;
    @XmlElementRef(name = "JSSZCRS", type = JAXBElement.class, required = false)
    protected JAXBElement<String> jsszcrs;
    @XmlElementRef(name = "ZGCS", type = JAXBElement.class, required = false)
    protected JAXBElement<String> zgcs;
    @XmlElementRef(name = "CLZZRQ", type = JAXBElement.class, required = false)
    protected JAXBElement<XMLGregorianCalendar> clzzrq;
    @XmlElementRef(name = "BZ", type = JAXBElement.class, required = false)
    protected JAXBElement<String> bz;
    @XmlElementRef(name = "CPSCDZ", type = JAXBElement.class, required = false)
    protected JAXBElement<String> cpscdz;
    @XmlElementRef(name = "CZRQ", type = JAXBElement.class, required = false)
    protected JAXBElement<XMLGregorianCalendar> czrq;
    @XmlElementRef(name = "FZRQ", type = JAXBElement.class, required = false)
    protected JAXBElement<XMLGregorianCalendar> fzrq;
    @XmlElementRef(name = "CLSCDWMC", type = JAXBElement.class, required = false)
    protected JAXBElement<String> clscdwmc;
    @XmlElementRef(name = "YH", type = JAXBElement.class, required = false)
    protected JAXBElement<String> yh;
    @XmlElementRef(name = "ZXZS", type = JAXBElement.class, required = false)
    protected JAXBElement<String> zxzs;
    @XmlElementRef(name = "CDDBJ", type = JAXBElement.class, required = false)
    protected JAXBElement<String> cddbj;
    @XmlElementRef(name = "PZXLH", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pzxlh;
    @XmlElementRef(name = "CREATETIME", type = JAXBElement.class, required = false)
    protected JAXBElement<XMLGregorianCalendar> createtime;
    @XmlElementRef(name = "VEHICLE_STATUS", type = JAXBElement.class, required = false)
    protected JAXBElement<String> vehiclestatus;
    @XmlElementRef(name = "RESPONSE_CODE", type = JAXBElement.class, required = false)
    protected JAXBElement<String> responsecode;
    @XmlElementRef(name = "CLIENT_HARDWARE_INFO", type = JAXBElement.class, required = false)
    protected JAXBElement<String> clienthardwareinfo;
    @XmlElementRef(name = "FEEDBACK_TIME", type = JAXBElement.class, required = false)
    protected JAXBElement<XMLGregorianCalendar> feedbacktime;
    @XmlElementRef(name = "HD_HOST", type = JAXBElement.class, required = false)
    protected JAXBElement<String> hdhost;
    @XmlElementRef(name = "HD_USER", type = JAXBElement.class, required = false)
    protected JAXBElement<String> hduser;
    @XmlElementRef(name = "UKEY", type = JAXBElement.class, required = false)
    protected JAXBElement<String> ukey;
    @XmlElementRef(name = "UPDATETIME", type = JAXBElement.class, required = false)
    protected JAXBElement<XMLGregorianCalendar> updatetime;
    @XmlElementRef(name = "UPSEND_TAG", type = JAXBElement.class, required = false)
    protected JAXBElement<String> upsendtag;
    @XmlElementRef(name = "VERCODE", type = JAXBElement.class, required = false)
    protected JAXBElement<String> vercode;
    @XmlElementRef(name = "VERSION", type = JAXBElement.class, required = false)
    protected JAXBElement<String> version;
    @XmlElementRef(name = "CLZTXX", type = JAXBElement.class, required = false)
    protected JAXBElement<String> clztxx;
    @XmlElementRef(name = "DYWYM", type = JAXBElement.class, required = false)
    protected JAXBElement<String> dywym;
    @XmlElementRef(name = "QYID", type = JAXBElement.class, required = false)
    protected JAXBElement<String> qyid;
    @XmlElementRef(name = "ZCHGZBH", type = JAXBElement.class, required = false)
    protected JAXBElement<String> zchgzbh;
    @XmlElementRef(name = "ZZBH", type = JAXBElement.class, required = false)
    protected JAXBElement<String> zzbh;
    @XmlElementRef(name = "CPH", type = JAXBElement.class, required = false)
    protected JAXBElement<String> cph;
    @XmlElementRef(name = "DPHGZBH", type = JAXBElement.class, required = false)
    protected JAXBElement<String> dphgzbh;
    @XmlElementRef(name = "DPID", type = JAXBElement.class, required = false)
    protected JAXBElement<String> dpid;
    @XmlElementRef(name = "GGSXRQ", type = JAXBElement.class, required = false)
    protected JAXBElement<XMLGregorianCalendar> ggsxrq;
    @XmlElementRef(name = "HZDCZFS", type = JAXBElement.class, required = false)
    protected JAXBElement<String> hzdczfs;
    @XmlElementRef(name = "HZDFS", type = JAXBElement.class, required = false)
    protected JAXBElement<String> hzdfs;
    @XmlElementRef(name = "PC", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pc;
    @XmlElementRef(name = "QYBZ", type = JAXBElement.class, required = false)
    protected JAXBElement<String> qybz;
    @XmlElementRef(name = "QYQTXX", type = JAXBElement.class, required = false)
    protected JAXBElement<String> qyqtxx;
    @XmlElementRef(name = "QZDCZFS", type = JAXBElement.class, required = false)
    protected JAXBElement<String> qzdczfs;
    @XmlElementRef(name = "QZDFS", type = JAXBElement.class, required = false)
    protected JAXBElement<String> qzdfs;
    @XmlElementRef(name = "WZHGZBH", type = JAXBElement.class, required = false)
    protected JAXBElement<String> wzhgzbh;
    @XmlElementRef(name = "JFPZID", type = JAXBElement.class, required = false)
    protected JAXBElement<String> jfpzid;
    @XmlElementRef(name = "VINBSYY", type = JAXBElement.class, required = false)
    protected JAXBElement<String> vinbsyy;
    @XmlElementRef(name = "ISCXNF", type = JAXBElement.class, required = false)
    protected JAXBElement<String> iscxnf;
    @XmlElementRef(name = "ZYZYCMSBS", type = JAXBElement.class, required = false)
    protected JAXBElement<String> zyzycmsbs;
    @XmlElementRef(name = "XNYQCJMSBS", type = JAXBElement.class, required = false)
    protected JAXBElement<String> xnyqcjmsbs;
    @XmlElementRef(name = "HDMSBS", type = JAXBElement.class, required = false)
    protected JAXBElement<String> hdmsbs;
    @XmlElementRef(name = "XNYQCZL", type = JAXBElement.class, required = false)
    protected JAXBElement<String> xnyqczl;
    @XmlElementRef(name = "STQYMC", type = JAXBElement.class, required = false)
    protected JAXBElement<String> stqymc;
    @XmlElementRef(name = "STSCDZ", type = JAXBElement.class, required = false)
    protected JAXBElement<String> stscdz;
    @XmlElementRef(name = "STSHXYDM", type = JAXBElement.class, required = false)
    protected JAXBElement<String> stshxydm;

    /**
     * 鑾峰彇hid灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getHID() {
        return hid;
    }

    /**
     * 璁剧疆hid灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setHID(JAXBElement<String> value) {
        this.hid = value;
    }

    /**
     * 鑾峰彇cjh灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getCJH() {
        return cjh;
    }

    /**
     * 璁剧疆cjh灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setCJH(JAXBElement<String> value) {
        this.cjh = value;
    }

    /**
     * 鑾峰彇clsbdh灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getCLSBDH() {
        return clsbdh;
    }

    /**
     * 璁剧疆clsbdh灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setCLSBDH(JAXBElement<String> value) {
        this.clsbdh = value;
    }

    /**
     * 鑾峰彇clzzqymc灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getCLZZQYMC() {
        return clzzqymc;
    }

    /**
     * 璁剧疆clzzqymc灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setCLZZQYMC(JAXBElement<String> value) {
        this.clzzqymc = value;
    }

    /**
     * 鑾峰彇cllx灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getCLLX() {
        return cllx;
    }

    /**
     * 璁剧疆cllx灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setCLLX(JAXBElement<String> value) {
        this.cllx = value;
    }

    /**
     * 鑾峰彇clmc灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getCLMC() {
        return clmc;
    }

    /**
     * 璁剧疆clmc灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setCLMC(JAXBElement<String> value) {
        this.clmc = value;
    }

    /**
     * 鑾峰彇clpp灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getCLPP() {
        return clpp;
    }

    /**
     * 璁剧疆clpp灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setCLPP(JAXBElement<String> value) {
        this.clpp = value;
    }

    /**
     * 鑾峰彇clxh灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getCLXH() {
        return clxh;
    }

    /**
     * 璁剧疆clxh灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setCLXH(JAXBElement<String> value) {
        this.clxh = value;
    }

    /**
     * 鑾峰彇csys灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getCSYS() {
        return csys;
    }

    /**
     * 璁剧疆csys灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setCSYS(JAXBElement<String> value) {
        this.csys = value;
    }

    /**
     * 鑾峰彇dpxh灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getDPXH() {
        return dpxh;
    }

    /**
     * 璁剧疆dpxh灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setDPXH(JAXBElement<String> value) {
        this.dpxh = value;
    }

    /**
     * 鑾峰彇fdjh灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getFDJH() {
        return fdjh;
    }

    /**
     * 璁剧疆fdjh灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setFDJH(JAXBElement<String> value) {
        this.fdjh = value;
    }

    /**
     * 鑾峰彇fdjxh灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getFDJXH() {
        return fdjxh;
    }

    /**
     * 璁剧疆fdjxh灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setFDJXH(JAXBElement<String> value) {
        this.fdjxh = value;
    }

    /**
     * 鑾峰彇rlzl灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getRLZL() {
        return rlzl;
    }

    /**
     * 璁剧疆rlzl灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setRLZL(JAXBElement<String> value) {
        this.rlzl = value;
    }

    /**
     * 鑾峰彇pfbz灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPFBZ() {
        return pfbz;
    }

    /**
     * 璁剧疆pfbz灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPFBZ(JAXBElement<String> value) {
        this.pfbz = value;
    }

    /**
     * 鑾峰彇pl灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPL() {
        return pl;
    }

    /**
     * 璁剧疆pl灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPL(JAXBElement<String> value) {
        this.pl = value;
    }

    /**
     * 鑾峰彇gl灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getGL() {
        return gl;
    }

    /**
     * 璁剧疆gl灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setGL(JAXBElement<String> value) {
        this.gl = value;
    }

    /**
     * 鑾峰彇zxxs灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getZXXS() {
        return zxxs;
    }

    /**
     * 璁剧疆zxxs灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setZXXS(JAXBElement<String> value) {
        this.zxxs = value;
    }

    /**
     * 鑾峰彇qlj灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getQLJ() {
        return qlj;
    }

    /**
     * 璁剧疆qlj灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setQLJ(JAXBElement<String> value) {
        this.qlj = value;
    }

    /**
     * 鑾峰彇hlj灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getHLJ() {
        return hlj;
    }

    /**
     * 璁剧疆hlj灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setHLJ(JAXBElement<String> value) {
        this.hlj = value;
    }

    /**
     * 鑾峰彇lts灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getLTS() {
        return lts;
    }

    /**
     * 璁剧疆lts灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setLTS(JAXBElement<String> value) {
        this.lts = value;
    }

    /**
     * 鑾峰彇ltgg灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getLTGG() {
        return ltgg;
    }

    /**
     * 璁剧疆ltgg灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setLTGG(JAXBElement<String> value) {
        this.ltgg = value;
    }

    /**
     * 鑾峰彇gbthps灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getGBTHPS() {
        return gbthps;
    }

    /**
     * 璁剧疆gbthps灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setGBTHPS(JAXBElement<String> value) {
        this.gbthps = value;
    }

    /**
     * 鑾峰彇zj灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getZJ() {
        return zj;
    }

    /**
     * 璁剧疆zj灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setZJ(JAXBElement<String> value) {
        this.zj = value;
    }

    /**
     * 鑾峰彇zh灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getZH() {
        return zh;
    }

    /**
     * 璁剧疆zh灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setZH(JAXBElement<String> value) {
        this.zh = value;
    }

    /**
     * 鑾峰彇zs灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getZS() {
        return zs;
    }

    /**
     * 璁剧疆zs灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setZS(JAXBElement<String> value) {
        this.zs = value;
    }

    /**
     * 鑾峰彇wkc灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getWKC() {
        return wkc;
    }

    /**
     * 璁剧疆wkc灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setWKC(JAXBElement<String> value) {
        this.wkc = value;
    }

    /**
     * 鑾峰彇wkk灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getWKK() {
        return wkk;
    }

    /**
     * 璁剧疆wkk灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setWKK(JAXBElement<String> value) {
        this.wkk = value;
    }

    /**
     * 鑾峰彇wkg灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getWKG() {
        return wkg;
    }

    /**
     * 璁剧疆wkg灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setWKG(JAXBElement<String> value) {
        this.wkg = value;
    }

    /**
     * 鑾峰彇hxnbc灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getHXNBC() {
        return hxnbc;
    }

    /**
     * 璁剧疆hxnbc灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setHXNBC(JAXBElement<String> value) {
        this.hxnbc = value;
    }

    /**
     * 鑾峰彇hxnbk灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getHXNBK() {
        return hxnbk;
    }

    /**
     * 璁剧疆hxnbk灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setHXNBK(JAXBElement<String> value) {
        this.hxnbk = value;
    }

    /**
     * 鑾峰彇hxnbg灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getHXNBG() {
        return hxnbg;
    }

    /**
     * 璁剧疆hxnbg灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setHXNBG(JAXBElement<String> value) {
        this.hxnbg = value;
    }

    /**
     * 鑾峰彇zzl灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getZZL() {
        return zzl;
    }

    /**
     * 璁剧疆zzl灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setZZL(JAXBElement<String> value) {
        this.zzl = value;
    }

    /**
     * 鑾峰彇edzzl灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getEDZZL() {
        return edzzl;
    }

    /**
     * 璁剧疆edzzl灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setEDZZL(JAXBElement<String> value) {
        this.edzzl = value;
    }

    /**
     * 鑾峰彇zbzl灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getZBZL() {
        return zbzl;
    }

    /**
     * 璁剧疆zbzl灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setZBZL(JAXBElement<String> value) {
        this.zbzl = value;
    }

    /**
     * 鑾峰彇zzllyxs灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getZZLLYXS() {
        return zzllyxs;
    }

    /**
     * 璁剧疆zzllyxs灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setZZLLYXS(JAXBElement<String> value) {
        this.zzllyxs = value;
    }

    /**
     * 鑾峰彇zqyzzl灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getZQYZZL() {
        return zqyzzl;
    }

    /**
     * 璁剧疆zqyzzl灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setZQYZZL(JAXBElement<String> value) {
        this.zqyzzl = value;
    }

    /**
     * 鑾峰彇edzk灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getEDZK() {
        return edzk;
    }

    /**
     * 璁剧疆edzk灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setEDZK(JAXBElement<String> value) {
        this.edzk = value;
    }

    /**
     * 鑾峰彇bgcazzdyxzzl灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getBGCAZZDYXZZL() {
        return bgcazzdyxzzl;
    }

    /**
     * 璁剧疆bgcazzdyxzzl灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setBGCAZZDYXZZL(JAXBElement<String> value) {
        this.bgcazzdyxzzl = value;
    }

    /**
     * 鑾峰彇jsszcrs灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getJSSZCRS() {
        return jsszcrs;
    }

    /**
     * 璁剧疆jsszcrs灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setJSSZCRS(JAXBElement<String> value) {
        this.jsszcrs = value;
    }

    /**
     * 鑾峰彇zgcs灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getZGCS() {
        return zgcs;
    }

    /**
     * 璁剧疆zgcs灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setZGCS(JAXBElement<String> value) {
        this.zgcs = value;
    }

    /**
     * 鑾峰彇clzzrq灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     *     
     */
    public JAXBElement<XMLGregorianCalendar> getCLZZRQ() {
        return clzzrq;
    }

    /**
     * 璁剧疆clzzrq灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     *     
     */
    public void setCLZZRQ(JAXBElement<XMLGregorianCalendar> value) {
        this.clzzrq = value;
    }

    /**
     * 鑾峰彇bz灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getBZ() {
        return bz;
    }

    /**
     * 璁剧疆bz灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setBZ(JAXBElement<String> value) {
        this.bz = value;
    }

    /**
     * 鑾峰彇cpscdz灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getCPSCDZ() {
        return cpscdz;
    }

    /**
     * 璁剧疆cpscdz灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setCPSCDZ(JAXBElement<String> value) {
        this.cpscdz = value;
    }

    /**
     * 鑾峰彇czrq灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     *     
     */
    public JAXBElement<XMLGregorianCalendar> getCZRQ() {
        return czrq;
    }

    /**
     * 璁剧疆czrq灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     *     
     */
    public void setCZRQ(JAXBElement<XMLGregorianCalendar> value) {
        this.czrq = value;
    }

    /**
     * 鑾峰彇fzrq灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     *     
     */
    public JAXBElement<XMLGregorianCalendar> getFZRQ() {
        return fzrq;
    }

    /**
     * 璁剧疆fzrq灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     *     
     */
    public void setFZRQ(JAXBElement<XMLGregorianCalendar> value) {
        this.fzrq = value;
    }

    /**
     * 鑾峰彇clscdwmc灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getCLSCDWMC() {
        return clscdwmc;
    }

    /**
     * 璁剧疆clscdwmc灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setCLSCDWMC(JAXBElement<String> value) {
        this.clscdwmc = value;
    }

    /**
     * 鑾峰彇yh灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getYH() {
        return yh;
    }

    /**
     * 璁剧疆yh灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setYH(JAXBElement<String> value) {
        this.yh = value;
    }

    /**
     * 鑾峰彇zxzs灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getZXZS() {
        return zxzs;
    }

    /**
     * 璁剧疆zxzs灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setZXZS(JAXBElement<String> value) {
        this.zxzs = value;
    }

    /**
     * 鑾峰彇cddbj灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getCDDBJ() {
        return cddbj;
    }

    /**
     * 璁剧疆cddbj灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setCDDBJ(JAXBElement<String> value) {
        this.cddbj = value;
    }

    /**
     * 鑾峰彇pzxlh灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPZXLH() {
        return pzxlh;
    }

    /**
     * 璁剧疆pzxlh灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPZXLH(JAXBElement<String> value) {
        this.pzxlh = value;
    }

    /**
     * 鑾峰彇createtime灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     *     
     */
    public JAXBElement<XMLGregorianCalendar> getCREATETIME() {
        return createtime;
    }

    /**
     * 璁剧疆createtime灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     *     
     */
    public void setCREATETIME(JAXBElement<XMLGregorianCalendar> value) {
        this.createtime = value;
    }

    /**
     * 鑾峰彇vehiclestatus灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getVEHICLESTATUS() {
        return vehiclestatus;
    }

    /**
     * 璁剧疆vehiclestatus灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setVEHICLESTATUS(JAXBElement<String> value) {
        this.vehiclestatus = value;
    }

    /**
     * 鑾峰彇responsecode灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getRESPONSECODE() {
        return responsecode;
    }

    /**
     * 璁剧疆responsecode灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setRESPONSECODE(JAXBElement<String> value) {
        this.responsecode = value;
    }

    /**
     * 鑾峰彇clienthardwareinfo灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getCLIENTHARDWAREINFO() {
        return clienthardwareinfo;
    }

    /**
     * 璁剧疆clienthardwareinfo灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setCLIENTHARDWAREINFO(JAXBElement<String> value) {
        this.clienthardwareinfo = value;
    }

    /**
     * 鑾峰彇feedbacktime灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     *     
     */
    public JAXBElement<XMLGregorianCalendar> getFEEDBACKTIME() {
        return feedbacktime;
    }

    /**
     * 璁剧疆feedbacktime灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     *     
     */
    public void setFEEDBACKTIME(JAXBElement<XMLGregorianCalendar> value) {
        this.feedbacktime = value;
    }

    /**
     * 鑾峰彇hdhost灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getHDHOST() {
        return hdhost;
    }

    /**
     * 璁剧疆hdhost灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setHDHOST(JAXBElement<String> value) {
        this.hdhost = value;
    }

    /**
     * 鑾峰彇hduser灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getHDUSER() {
        return hduser;
    }

    /**
     * 璁剧疆hduser灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setHDUSER(JAXBElement<String> value) {
        this.hduser = value;
    }

    /**
     * 鑾峰彇ukey灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getUKEY() {
        return ukey;
    }

    /**
     * 璁剧疆ukey灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setUKEY(JAXBElement<String> value) {
        this.ukey = value;
    }

    /**
     * 鑾峰彇updatetime灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     *     
     */
    public JAXBElement<XMLGregorianCalendar> getUPDATETIME() {
        return updatetime;
    }

    /**
     * 璁剧疆updatetime灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     *     
     */
    public void setUPDATETIME(JAXBElement<XMLGregorianCalendar> value) {
        this.updatetime = value;
    }

    /**
     * 鑾峰彇upsendtag灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getUPSENDTAG() {
        return upsendtag;
    }

    /**
     * 璁剧疆upsendtag灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setUPSENDTAG(JAXBElement<String> value) {
        this.upsendtag = value;
    }

    /**
     * 鑾峰彇vercode灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getVERCODE() {
        return vercode;
    }

    /**
     * 璁剧疆vercode灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setVERCODE(JAXBElement<String> value) {
        this.vercode = value;
    }

    /**
     * 鑾峰彇version灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getVERSION() {
        return version;
    }

    /**
     * 璁剧疆version灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setVERSION(JAXBElement<String> value) {
        this.version = value;
    }

    /**
     * 鑾峰彇clztxx灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getCLZTXX() {
        return clztxx;
    }

    /**
     * 璁剧疆clztxx灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setCLZTXX(JAXBElement<String> value) {
        this.clztxx = value;
    }

    /**
     * 鑾峰彇dywym灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getDYWYM() {
        return dywym;
    }

    /**
     * 璁剧疆dywym灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setDYWYM(JAXBElement<String> value) {
        this.dywym = value;
    }

    /**
     * 鑾峰彇qyid灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getQYID() {
        return qyid;
    }

    /**
     * 璁剧疆qyid灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setQYID(JAXBElement<String> value) {
        this.qyid = value;
    }

    /**
     * 鑾峰彇zchgzbh灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getZCHGZBH() {
        return zchgzbh;
    }

    /**
     * 璁剧疆zchgzbh灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setZCHGZBH(JAXBElement<String> value) {
        this.zchgzbh = value;
    }

    /**
     * 鑾峰彇zzbh灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getZZBH() {
        return zzbh;
    }

    /**
     * 璁剧疆zzbh灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setZZBH(JAXBElement<String> value) {
        this.zzbh = value;
    }

    /**
     * 鑾峰彇cph灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getCPH() {
        return cph;
    }

    /**
     * 璁剧疆cph灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setCPH(JAXBElement<String> value) {
        this.cph = value;
    }

    /**
     * 鑾峰彇dphgzbh灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getDPHGZBH() {
        return dphgzbh;
    }

    /**
     * 璁剧疆dphgzbh灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setDPHGZBH(JAXBElement<String> value) {
        this.dphgzbh = value;
    }

    /**
     * 鑾峰彇dpid灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getDPID() {
        return dpid;
    }

    /**
     * 璁剧疆dpid灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setDPID(JAXBElement<String> value) {
        this.dpid = value;
    }

    /**
     * 鑾峰彇ggsxrq灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     *     
     */
    public JAXBElement<XMLGregorianCalendar> getGGSXRQ() {
        return ggsxrq;
    }

    /**
     * 璁剧疆ggsxrq灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}
     *     
     */
    public void setGGSXRQ(JAXBElement<XMLGregorianCalendar> value) {
        this.ggsxrq = value;
    }

    /**
     * 鑾峰彇hzdczfs灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getHZDCZFS() {
        return hzdczfs;
    }

    /**
     * 璁剧疆hzdczfs灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setHZDCZFS(JAXBElement<String> value) {
        this.hzdczfs = value;
    }

    /**
     * 鑾峰彇hzdfs灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getHZDFS() {
        return hzdfs;
    }

    /**
     * 璁剧疆hzdfs灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setHZDFS(JAXBElement<String> value) {
        this.hzdfs = value;
    }

    /**
     * 鑾峰彇pc灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPC() {
        return pc;
    }

    /**
     * 璁剧疆pc灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPC(JAXBElement<String> value) {
        this.pc = value;
    }

    /**
     * 鑾峰彇qybz灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getQYBZ() {
        return qybz;
    }

    /**
     * 璁剧疆qybz灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setQYBZ(JAXBElement<String> value) {
        this.qybz = value;
    }

    /**
     * 鑾峰彇qyqtxx灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getQYQTXX() {
        return qyqtxx;
    }

    /**
     * 璁剧疆qyqtxx灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setQYQTXX(JAXBElement<String> value) {
        this.qyqtxx = value;
    }

    /**
     * 鑾峰彇qzdczfs灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getQZDCZFS() {
        return qzdczfs;
    }

    /**
     * 璁剧疆qzdczfs灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setQZDCZFS(JAXBElement<String> value) {
        this.qzdczfs = value;
    }

    /**
     * 鑾峰彇qzdfs灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getQZDFS() {
        return qzdfs;
    }

    /**
     * 璁剧疆qzdfs灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setQZDFS(JAXBElement<String> value) {
        this.qzdfs = value;
    }

    /**
     * 鑾峰彇wzhgzbh灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getWZHGZBH() {
        return wzhgzbh;
    }

    /**
     * 璁剧疆wzhgzbh灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setWZHGZBH(JAXBElement<String> value) {
        this.wzhgzbh = value;
    }

    /**
     * 鑾峰彇jfpzid灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getJFPZID() {
        return jfpzid;
    }

    /**
     * 璁剧疆jfpzid灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setJFPZID(JAXBElement<String> value) {
        this.jfpzid = value;
    }

    /**
     * 鑾峰彇vinbsyy灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getVINBSYY() {
        return vinbsyy;
    }

    /**
     * 璁剧疆vinbsyy灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setVINBSYY(JAXBElement<String> value) {
        this.vinbsyy = value;
    }

    /**
     * 鑾峰彇iscxnf灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getISCXNF() {
        return iscxnf;
    }

    /**
     * 璁剧疆iscxnf灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setISCXNF(JAXBElement<String> value) {
        this.iscxnf = value;
    }

    /**
     * 鑾峰彇zyzycmsbs灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getZYZYCMSBS() {
        return zyzycmsbs;
    }

    /**
     * 璁剧疆zyzycmsbs灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setZYZYCMSBS(JAXBElement<String> value) {
        this.zyzycmsbs = value;
    }

    /**
     * 鑾峰彇xnyqcjmsbs灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getXNYQCJMSBS() {
        return xnyqcjmsbs;
    }

    /**
     * 璁剧疆xnyqcjmsbs灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setXNYQCJMSBS(JAXBElement<String> value) {
        this.xnyqcjmsbs = value;
    }

    /**
     * 鑾峰彇hdmsbs灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getHDMSBS() {
        return hdmsbs;
    }

    /**
     * 璁剧疆hdmsbs灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setHDMSBS(JAXBElement<String> value) {
        this.hdmsbs = value;
    }

    /**
     * 鑾峰彇xnyqczl灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getXNYQCZL() {
        return xnyqczl;
    }

    /**
     * 璁剧疆xnyqczl灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setXNYQCZL(JAXBElement<String> value) {
        this.xnyqczl = value;
    }

    /**
     * 鑾峰彇stqymc灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getSTQYMC() {
        return stqymc;
    }

    /**
     * 璁剧疆stqymc灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setSTQYMC(JAXBElement<String> value) {
        this.stqymc = value;
    }

    /**
     * 鑾峰彇stscdz灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getSTSCDZ() {
        return stscdz;
    }

    /**
     * 璁剧疆stscdz灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setSTSCDZ(JAXBElement<String> value) {
        this.stscdz = value;
    }

    /**
     * 鑾峰彇stshxydm灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getSTSHXYDM() {
        return stshxydm;
    }

    /**
     * 璁剧疆stshxydm灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setSTSHXYDM(JAXBElement<String> value) {
        this.stshxydm = value;
    }

}
