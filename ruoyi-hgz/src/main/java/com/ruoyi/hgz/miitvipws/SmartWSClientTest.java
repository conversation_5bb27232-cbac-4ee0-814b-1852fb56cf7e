package com.ruoyi.hgz.miitvipws;

import com.ruoyi.hgz.miitvipws.client.CertificateInfo;

import java.util.List;

/**
 * SmartWSClient测试类
 * 验证智能WebService客户端的各种策略
 */
public class SmartWSClientTest {
    
    public static void main(String[] args) {
        System.out.println("=== SmartWSClient 智能WebService客户端测试 ===\n");
        
        // 测试连接
        testConnection();
        
        // 测试查询
        testQuery();
        
        // 对比测试
        compareWithOtherClients();
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    /**
     * 测试连接
     */
    private static void testConnection() {
        System.out.println("1. 连接测试");
        System.out.println("----------------------------------------");
        
        try {
            System.out.println("正在测试HelloWorld...");
            String result = SmartWSClient.helloWorld();
            
            if (result != null && !result.trim().isEmpty()) {
                System.out.println("✓ 连接测试成功");
                System.out.println("响应内容: " + result);
            } else {
                System.out.println("⚠️ 连接测试返回空结果");
            }
            
        } catch (Exception e) {
            System.out.println("✗ 连接测试失败: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 测试查询功能
     */
    private static void testQuery() {
        System.out.println("2. 查询功能测试");
        System.out.println("----------------------------------------");
        
        try {
            // 测试参数
            String username = "HX231008U001";
            String password = "D#$>sy38";
            String wzhgzbh = "";
            String clsbdh = "LGHY5J2G2RC053149";
            
            System.out.println("查询参数:");
            System.out.println("  用户名: " + username);
            System.out.println("  密码: " + password);
            System.out.println("  网证合格证编号: " + wzhgzbh);
            System.out.println("  车辆识别代号: " + clsbdh);
            System.out.println();
            
            System.out.println("正在执行智能查询...");
            long startTime = System.currentTimeMillis();
            
            List<CertificateInfo> results = SmartWSClient.queryCertificateSingle(
                username, password, wzhgzbh, clsbdh);
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            System.out.println("查询完成，耗时: " + duration + "ms");
            System.out.println();
            
            if (results != null && !results.isEmpty()) {
                System.out.println("✓ 查询成功");
                System.out.println("返回记录数: " + results.size());
                System.out.println("返回类型: List<CertificateInfo> (真正的实体对象)");
                System.out.println();
                
                // 显示详细信息
                for (int i = 0; i < Math.min(results.size(), 2); i++) {
                    CertificateInfo cert = results.get(i);
                    System.out.println("第 " + (i + 1) + " 条记录:");
                    
                    if (cert.getWZHGZBH() != null && cert.getWZHGZBH().getValue() != null) {
                        System.out.println("  ✓ 网证合格证编号: " + cert.getWZHGZBH().getValue());
                    }
                    
                    if (cert.getCLSBDH() != null && cert.getCLSBDH().getValue() != null) {
                        System.out.println("  ✓ 车辆识别代号: " + cert.getCLSBDH().getValue());
                    }
                    
                    if (cert.getCLZZQYMC() != null && cert.getCLZZQYMC().getValue() != null) {
                        System.out.println("  ✓ 制造企业名称: " + cert.getCLZZQYMC().getValue());
                    }
                    
                    if (cert.getCLLX() != null && cert.getCLLX().getValue() != null) {
                        System.out.println("  ✓ 车辆类型: " + cert.getCLLX().getValue());
                    }
                    
                    if (cert.getCLMC() != null && cert.getCLMC().getValue() != null) {
                        System.out.println("  ✓ 车辆名称: " + cert.getCLMC().getValue());
                    }
                    
                    System.out.println();
                }
                
                // 验证实体对象的完整性
                System.out.println("🔍 实体对象验证:");
                CertificateInfo firstCert = results.get(0);
                System.out.println("  对象类型: " + firstCert.getClass().getSimpleName());
                System.out.println("  是否为CertificateInfo实例: " + (firstCert instanceof CertificateInfo));
                System.out.println("  可以直接传递给业务层: ✓");
                
            } else {
                System.out.println("⚠️ 查询成功但未找到匹配记录");
            }
            
        } catch (Exception e) {
            System.out.println("✗ 查询失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println();
    }
    
    /**
     * 对比测试不同客户端的性能和可靠性
     */
    private static void compareWithOtherClients() {
        System.out.println("3. 客户端对比测试");
        System.out.println("----------------------------------------");
        
        String username = "HX231008U001";
        String password = "D#$>sy38";
        String wzhgzbh = "";
        String clsbdh = "LGHY5J2G2RC053149";
        
        // 测试SmartWSClient
        System.out.println("SmartWSClient (智能客户端):");
        testClient("SmartWSClient", () -> {
            return SmartWSClient.queryCertificateSingle(username, password, wzhgzbh, clsbdh);
        });
        
        // 测试ProductionWSClient
        System.out.println("ProductionWSClient (标准客户端):");
        testClient("ProductionWSClient", () -> {
            return ProductionWSClient.queryCertificateSingle(username, password, wzhgzbh, clsbdh);
        });
        
        // 测试FixedWSClient
        System.out.println("FixedWSClient (修复客户端):");
        testClient("FixedWSClient", () -> {
            return FixedWSClient.queryCertificateSingle(username, password, wzhgzbh, clsbdh);
        });
        
        // 测试DirectWSClient
        System.out.println("DirectWSClient (直接客户端):");
        testClient("DirectWSClient", () -> {
            return DirectWSClient.queryCertificateSingle(username, password, wzhgzbh, clsbdh);
        });
        
        System.out.println();
        System.out.println("📊 总结:");
        System.out.println("SmartWSClient 的优势:");
        System.out.println("  ✓ 多策略自动切换，提高成功率");
        System.out.println("  ✓ 优先使用标准JAXB映射，避免手动解析");
        System.out.println("  ✓ 自动降级机制，确保服务可用性");
        System.out.println("  ✓ 返回真正的实体对象，便于业务处理");
        System.out.println("  ✓ 统一的接口，隐藏复杂性");
    }
    
    /**
     * 测试单个客户端
     */
    private static void testClient(String clientName, ClientTester tester) {
        try {
            long startTime = System.currentTimeMillis();
            List<CertificateInfo> results = tester.test();
            long endTime = System.currentTimeMillis();
            
            if (results != null && !results.isEmpty()) {
                System.out.println("  ✓ 成功 - 耗时: " + (endTime - startTime) + "ms, 记录数: " + results.size());
                
                // 验证数据质量
                CertificateInfo first = results.get(0);
                boolean hasValidData = (first.getWZHGZBH() != null && first.getWZHGZBH().getValue() != null) ||
                                     (first.getCLSBDH() != null && first.getCLSBDH().getValue() != null) ||
                                     (first.getCLZZQYMC() != null && first.getCLZZQYMC().getValue() != null);
                
                if (hasValidData) {
                    System.out.println("    数据质量: ✓ 包含有效字段");
                } else {
                    System.out.println("    数据质量: ⚠️ 字段可能为空");
                }
            } else {
                System.out.println("  ⚠️ 成功但无数据 - 耗时: " + (endTime - startTime) + "ms");
            }
            
        } catch (Exception e) {
            System.out.println("  ✗ 失败: " + e.getMessage());
        }
    }
    
    /**
     * 客户端测试接口
     */
    @FunctionalInterface
    private interface ClientTester {
        List<CertificateInfo> test() throws Exception;
    }
}
