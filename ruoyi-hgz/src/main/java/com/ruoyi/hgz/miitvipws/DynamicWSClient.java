package com.ruoyi.hgz.miitvipws;

import javax.net.ssl.*;
import javax.xml.namespace.QName;
import javax.xml.soap.*;
import javax.xml.ws.Dispatch;
import javax.xml.ws.Service;
import javax.xml.ws.soap.SOAPBinding;
import java.net.URL;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;

/**
 * 动态WebService客户端
 * 使用SOAP消息直接调用，避免WSDL依赖问题
 */
public class DynamicWSClient {
    
    private static final String SERVICE_URL = "https://hgz.miit.gov.cn/enterprise/services/CertificateRequestVIPService";
    private static final String NAMESPACE_URI = "http://www.vidc.info/certificate/operation/";
    private static final String SERVICE_NAME = "CertificateRequestVIP";
    private static final String PORT_NAME = "CertificateRequestVIPServiceImplPort";
    
    private static volatile Dispatch<SOAPMessage> dispatch;
    private static volatile boolean sslInitialized = false;
    
    /**
     * 初始化SSL配置
     */
    private static synchronized void initSSL() throws Exception {
        if (sslInitialized) {
            return;
        }
        
        try {
            // 创建信任所有证书的TrustManager
            TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                }
            };
            
            // 安装信任所有证书的TrustManager
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
            
            // 创建主机名验证器
            HostnameVerifier allHostsValid = (hostname, session) -> true;
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);
            
            sslInitialized = true;
            System.out.println("SSL配置初始化成功");
        } catch (Exception e) {
            System.err.println("SSL配置初始化失败: " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * 获取Dispatch客户端
     */
    public static synchronized Dispatch<SOAPMessage> getDispatch() throws Exception {
        if (dispatch == null) {
            initSSL();
            
            QName serviceQName = new QName(NAMESPACE_URI, SERVICE_NAME);
            QName portQName = new QName(NAMESPACE_URI, PORT_NAME);
            
            Service service = Service.create(serviceQName);
            service.addPort(portQName, SOAPBinding.SOAP11HTTP_BINDING, SERVICE_URL);
            
            dispatch = service.createDispatch(portQName, SOAPMessage.class, Service.Mode.MESSAGE);
            
            System.out.println("Dispatch客户端创建成功");
        }
        return dispatch;
    }
    
    /**
     * 创建HelloWorld SOAP消息
     */
    private static SOAPMessage createHelloWorldMessage() throws Exception {
        MessageFactory messageFactory = MessageFactory.newInstance();
        SOAPMessage soapMessage = messageFactory.createMessage();
        SOAPPart soapPart = soapMessage.getSOAPPart();
        
        SOAPEnvelope envelope = soapPart.getEnvelope();
        envelope.addNamespaceDeclaration("tns", NAMESPACE_URI);
        
        SOAPHeader header = envelope.getHeader();
        SOAPBody body = envelope.getBody();
        
        // 创建HelloWorld元素
        SOAPElement helloWorldElement = body.addChildElement("HelloWorld", "tns");
        
        soapMessage.saveChanges();
        return soapMessage;
    }
    
    /**
     * 创建查询SOAP消息
     */
    private static SOAPMessage createQueryMessage(String username, String password, String wzhgzbh, String clsbdh) throws Exception {
        MessageFactory messageFactory = MessageFactory.newInstance();
        SOAPMessage soapMessage = messageFactory.createMessage();
        SOAPPart soapPart = soapMessage.getSOAPPart();
        
        SOAPEnvelope envelope = soapPart.getEnvelope();
        envelope.addNamespaceDeclaration("tns", NAMESPACE_URI);
        
        SOAPHeader header = envelope.getHeader();
        SOAPBody body = envelope.getBody();
        
        // 创建QueryCertificateSingle元素
        SOAPElement queryElement = body.addChildElement("QueryCertificateSingle", "tns");
        
        // 添加参数
        SOAPElement usernameElement = queryElement.addChildElement("username", "tns");
        usernameElement.addTextNode(username);
        
        SOAPElement passwordElement = queryElement.addChildElement("password", "tns");
        passwordElement.addTextNode(password);
        
        SOAPElement wzhgzbhElement = queryElement.addChildElement("wzhgzbh", "tns");
        wzhgzbhElement.addTextNode(wzhgzbh);
        
        SOAPElement clsbdhElement = queryElement.addChildElement("clsbdh", "tns");
        clsbdhElement.addTextNode(clsbdh);
        
        soapMessage.saveChanges();
        return soapMessage;
    }
    
    /**
     * 测试HelloWorld方法
     */
    public static String testHelloWorld() {
        try {
            System.out.println("正在调用HelloWorld方法...");
            
            Dispatch<SOAPMessage> client = getDispatch();
            SOAPMessage request = createHelloWorldMessage();
            
            System.out.println("发送SOAP请求...");
            SOAPMessage response = client.invoke(request);
            
            // 解析响应
            SOAPBody responseBody = response.getSOAPBody();
            String result = extractTextContent(responseBody);
            
            System.out.println("HelloWorld调用成功");
            return result;
            
        } catch (Exception e) {
            System.err.println("HelloWorld调用失败: " + e.getMessage());
            e.printStackTrace();
            return "Error: " + e.getMessage();
        }
    }
    
    /**
     * 测试查询方法
     */
    public static String testQuery(String username, String password, String wzhgzbh, String clsbdh) {
        try {
            System.out.println("正在调用查询方法...");
            System.out.println("参数: username=" + username + ", password=" + password + 
                             ", wzhgzbh=" + wzhgzbh + ", clsbdh=" + clsbdh);
            
            Dispatch<SOAPMessage> client = getDispatch();
            SOAPMessage request = createQueryMessage(username, password, wzhgzbh, clsbdh);
            
            System.out.println("发送SOAP请求...");
            SOAPMessage response = client.invoke(request);
            
            // 解析响应
            SOAPBody responseBody = response.getSOAPBody();
            String result = extractTextContent(responseBody);
            
            System.out.println("查询调用成功");
            return result;
            
        } catch (Exception e) {
            System.err.println("查询调用失败: " + e.getMessage());
            e.printStackTrace();
            return "Error: " + e.getMessage();
        }
    }
    
    /**
     * 提取SOAP响应中的文本内容
     */
    private static String extractTextContent(SOAPBody body) throws Exception {
        StringBuilder result = new StringBuilder();
        extractTextFromNode(body, result);
        return result.toString();
    }
    
    /**
     * 递归提取节点中的文本内容
     */
    private static void extractTextFromNode(org.w3c.dom.Node node, StringBuilder result) {
        if (node.getNodeType() == org.w3c.dom.Node.TEXT_NODE) {
            String text = node.getNodeValue().trim();
            if (!text.isEmpty()) {
                if (result.length() > 0) {
                    result.append(" ");
                }
                result.append(text);
            }
        } else if (node.getNodeType() == org.w3c.dom.Node.ELEMENT_NODE) {
            org.w3c.dom.NodeList children = node.getChildNodes();
            for (int i = 0; i < children.getLength(); i++) {
                extractTextFromNode(children.item(i), result);
            }
        }
    }
    
    /**
     * 重置连接
     */
    public static synchronized void resetConnection() {
        dispatch = null;
        System.out.println("Dispatch连接已重置");
    }
    
    /**
     * 主测试方法
     */
    public static void main(String[] args) {
        System.out.println("=== 动态WebService客户端测试 ===");
        
        // 测试HelloWorld
        System.out.println("\n1. 测试HelloWorld:");
        String helloResult = testHelloWorld();
        System.out.println("结果: " + helloResult);
        
        // 测试查询
        System.out.println("\n2. 测试查询:");
        String queryResult = testQuery("HX231008U001", "D#$>sy38", "", "LGHY5J2G2RC053149");
        System.out.println("结果: " + queryResult);
        
        System.out.println("\n=== 测试完成 ===");
    }
}
