package com.ruoyi.hgz.miitvipws;

import com.ruoyi.hgz.miitvipws.client.CertificateInfo;
import com.ruoyi.hgz.miitvipws.client.CertificateRequestVIP;

import java.util.Date;
import java.util.List;

/**
 * 简单的WebService测试类
 * 不依赖外部日志框架
 */
public class SimpleWSTest {
    
    public static void main(String[] args) {
        System.out.println("=== WebService连接测试开始 ===");
        System.out.println("测试时间: " + new Date());
        
        // 测试1: 使用修复后的WSClient
        testOriginalWSClient();
        
        // 测试2: 使用ManualWSClient
        testManualWSClient();
        
        System.out.println("=== WebService连接测试结束 ===");
    }
    
    /**
     * 测试原始的WSClient（已修复）
     */
    private static void testOriginalWSClient() {
        System.out.println("\n--- 测试1: 使用修复后的WSClient ---");
        
        try {
            System.out.println("正在创建WebService连接...");
            CertificateRequestVIP port = WSClient.getPort();
            System.out.println("✓ WebService连接创建成功");
            
            System.out.println("正在调用HelloWorld方法...");
            String result = port.helloWorld();
            System.out.println("✓ HelloWorld调用成功");
            System.out.println("服务器响应: " + result);
            
        } catch (Exception e) {
            System.out.println("✗ WSClient测试失败");
            System.out.println("错误信息: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试ManualWSClient
     */
    private static void testManualWSClient() {
        System.out.println("\n--- 测试2: 使用ManualWSClient ---");
        
        try {
            System.out.println("正在创建WebService连接...");
            CertificateRequestVIP port = ManualWSClient.getPort();
            System.out.println("✓ WebService连接创建成功");
            
            System.out.println("正在调用HelloWorld方法...");
            String result = port.helloWorld();
            System.out.println("✓ HelloWorld调用成功");
            System.out.println("服务器响应: " + result);
            
        } catch (Exception e) {
            System.out.println("✗ ManualWSClient测试失败");
            System.out.println("错误信息: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试查询功能
     */
    public static void testQuery() {
        System.out.println("\n--- 测试3: 查询功能测试 ---");
        
        try {
            // 使用测试参数进行查询
            String username = "HX231008U001";
            String password = "D#$>sy38";
            String wzhgzbh = "";
            String clsbdh = "LGHY5J2G2RC053149";
            
            System.out.println("查询参数:");
            System.out.println("  用户名: " + username);
            System.out.println("  密码: " + password);
            System.out.println("  网证合格证编号: " + wzhgzbh);
            System.out.println("  车辆识别代号: " + clsbdh);
            
            CertificateRequestVIP port = WSClient.getPort();
            List<CertificateInfo> results = port.queryCertificateSingle(
                username, password, wzhgzbh, clsbdh);
            
            if (results != null && !results.isEmpty()) {
                System.out.println("✓ 查询成功");
                System.out.println("返回记录数: " + results.size());
                
                // 显示第一条记录的详细信息
                CertificateInfo firstRecord = results.get(0);
                System.out.println("第一条记录信息:");
                if (firstRecord.getWZHGZBH() != null && firstRecord.getWZHGZBH().getValue() != null) {
                    System.out.println("  网证合格证编号: " + firstRecord.getWZHGZBH().getValue());
                }
                if (firstRecord.getCLSBDH() != null && firstRecord.getCLSBDH().getValue() != null) {
                    System.out.println("  车辆识别代号: " + firstRecord.getCLSBDH().getValue());
                }
            } else {
                System.out.println("✓ 查询成功，但未找到匹配记录");
            }
            
        } catch (Exception e) {
            System.out.println("✗ 查询测试失败");
            System.out.println("错误信息: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 性能测试
     */
    public static void performanceTest() {
        System.out.println("\n--- 性能测试 ---");
        
        int testCount = 3;
        long totalTime = 0;
        int successCount = 0;
        
        for (int i = 1; i <= testCount; i++) {
            System.out.println("第 " + i + " 次测试:");
            long startTime = System.currentTimeMillis();
            
            try {
                CertificateRequestVIP port = WSClient.getPort();
                String result = port.helloWorld();
                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;
                totalTime += duration;
                successCount++;
                
                System.out.println("  ✓ 成功 - 耗时: " + duration + "ms - 响应: " + result);
            } catch (Exception e) {
                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;
                System.out.println("  ✗ 失败 - 耗时: " + duration + "ms - " + e.getMessage());
            }
        }
        
        System.out.println("\n性能测试结果:");
        System.out.println("  总测试次数: " + testCount);
        System.out.println("  成功次数: " + successCount);
        System.out.println("  成功率: " + (successCount * 100.0 / testCount) + "%");
        if (successCount > 0) {
            System.out.println("  平均响应时间: " + (totalTime / successCount) + "ms");
        }
    }
}
