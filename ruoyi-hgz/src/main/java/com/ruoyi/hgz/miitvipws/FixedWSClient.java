package com.ruoyi.hgz.miitvipws;

import com.ruoyi.hgz.miitvipws.client.CertificateInfo;
import com.ruoyi.hgz.miitvipws.client.ObjectFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 修复后的WebService客户端
 * 使用DirectWSClient作为底层实现，但返回结构化的CertificateInfo对象
 * 解决了WSDL依赖问题，同时保持了便利的接口
 */
public class FixedWSClient {
    
    private static final ObjectFactory factory = new ObjectFactory();
    
    /**
     * 测试连接
     * @return 服务器响应消息
     */
    public static String helloWorld() {
        try {
            return DirectWSClient.helloWorld();
        } catch (Exception e) {
            throw new RuntimeException("HelloWorld调用失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 查询单个证书信息
     *
     * @param username 用户名
     * @param password 密码
     * @param wzhgzbh 网证合格证编号（可为空）
     * @param clsbdh 车辆识别代号
     * @return 证书信息列表
     */
    public static List<CertificateInfo> queryCertificateSingle(String username, String password,
                                                               String wzhgzbh, String clsbdh) {
        try {
            // 直接调用DirectWSClient，返回CertificateInfo对象
            return DirectWSClient.queryCertificateSingle(username, password, wzhgzbh, clsbdh);

        } catch (Exception e) {
            throw new RuntimeException("查询证书信息失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 按条件查询证书信息
     * 
     * @param username 用户名
     * @param password 密码
     * @param wzhgzbh 网证合格证编号
     * @param clsbdh 车辆识别代号
     * @param clxh 车辆型号
     * @param status 状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pagesite 页码
     * @param pageSize 页大小
     * @return 证书信息列表
     */
    public static List<CertificateInfo> queryByCondition(String username, String password,
                                                         String wzhgzbh, String clsbdh, String clxh,
                                                         String status, String startTime, String endTime,
                                                         int pagesite, int pageSize) {
        // 注意：DirectWSClient目前只支持queryCertificateSingle方法
        // 这里暂时使用相同的实现，实际项目中需要扩展DirectWSClient
        return queryCertificateSingle(username, password, wzhgzbh, clsbdh);
    }
    

    
    /**
     * 重置连接
     */
    public static void resetConnection() {
        DirectWSClient.resetConnection();
    }
    
    /**
     * 获取服务信息
     */
    public static String getServiceInfo() {
        return "FixedWSClient - 基于DirectWSClient的结构化WebService客户端\n" +
               "解决了WSDL依赖问题，直接返回CertificateInfo对象\n" +
               "底层使用SOAP消息和JAXB对象映射";
    }
    
    /**
     * 验证连接是否正常
     */
    public static boolean validateConnection() {
        try {
            String result = helloWorld();
            return result != null && result.contains("Hello");
        } catch (Exception e) {
            return false;
        }
    }
}
