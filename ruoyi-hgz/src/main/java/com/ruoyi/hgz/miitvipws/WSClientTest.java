package com.ruoyi.hgz.miitvipws;

import com.ruoyi.hgz.miitvipws.client.CertificateInfo;
import com.ruoyi.hgz.miitvipws.client.CertificateRequestVIP;

import java.util.Date;
import java.util.List;

/**
 * WSClient测试类
 * 验证修复后的WSClient能否正常返回CertificateInfo对象
 */
public class WSClientTest {
    
    public static void main(String[] args) {
        System.out.println("=== WSClient修复验证测试 ===");
        System.out.println("测试时间: " + new Date());
        
        // 测试1: 基本连接测试
        testConnection();
        
        // 测试2: HelloWorld方法测试
        testHelloWorld();
        
        // 测试3: 查询方法测试
        testQuery();
        
        // 测试4: 性能测试
        performanceTest();
        
        // 测试5: 对比测试
        comparisonTest();
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    /**
     * 测试连接创建
     */
    private static void testConnection() {
        System.out.println("\n--- 测试1: 连接创建测试 ---");
        
        try {
            System.out.println("正在创建WebService连接...");
            CertificateRequestVIP port = WSClient.getPort();
            
            if (port != null) {
                System.out.println("✓ 连接创建成功");
                System.out.println("端口类型: " + port.getClass().getName());
            } else {
                System.out.println("✗ 连接创建失败 - 返回null");
            }
            
        } catch (Exception e) {
            System.out.println("✗ 连接创建失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试HelloWorld方法
     */
    private static void testHelloWorld() {
        System.out.println("\n--- 测试2: HelloWorld方法测试 ---");
        
        try {
            System.out.println("正在调用HelloWorld方法...");
            CertificateRequestVIP port = WSClient.getPort();
            String result = port.helloWorld();
            
            System.out.println("✓ HelloWorld调用成功");
            System.out.println("返回结果: " + result);
            
        } catch (Exception e) {
            System.out.println("✗ HelloWorld调用失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试查询方法
     */
    private static void testQuery() {
        System.out.println("\n--- 测试3: 查询方法测试 ---");
        
        try {
            // 测试参数
            String username = "HX231008U001";
            String password = "D#$>sy38";
            String wzhgzbh = "";
            String clsbdh = "LGHY5J2G2RC053149";
            
            System.out.println("查询参数:");
            System.out.println("  用户名: " + username);
            System.out.println("  密码: " + password);
            System.out.println("  网证合格证编号: " + wzhgzbh);
            System.out.println("  车辆识别代号: " + clsbdh);
            
            System.out.println("正在调用查询方法...");
            CertificateRequestVIP port = WSClient.getPort();
            List<CertificateInfo> results = port.queryCertificateSingle(username, password, wzhgzbh, clsbdh);
            
            if (results != null) {
                System.out.println("✓ 查询调用成功");
                System.out.println("返回记录数: " + results.size());
                
                if (!results.isEmpty()) {
                    System.out.println("\n证书信息详情:");
                    for (int i = 0; i < Math.min(results.size(), 3); i++) {
                        CertificateInfo cert = results.get(i);
                        System.out.println("第 " + (i + 1) + " 条记录:");
                        
                        // 网证合格证编号
                        if (cert.getWZHGZBH() != null && cert.getWZHGZBH().getValue() != null) {
                            System.out.println("  网证合格证编号: " + cert.getWZHGZBH().getValue());
                        }
                        
                        // 车辆识别代号
                        if (cert.getCLSBDH() != null && cert.getCLSBDH().getValue() != null) {
                            System.out.println("  车辆识别代号: " + cert.getCLSBDH().getValue());
                        }
                        
                        // 制造企业名称
                        if (cert.getCLZZQYMC() != null && cert.getCLZZQYMC().getValue() != null) {
                            System.out.println("  制造企业名称: " + cert.getCLZZQYMC().getValue());
                        }
                        
                        // 车辆类型
                        if (cert.getCLLX() != null && cert.getCLLX().getValue() != null) {
                            System.out.println("  车辆类型: " + cert.getCLLX().getValue());
                        }
                        
                        // 车辆名称
                        if (cert.getCLMC() != null && cert.getCLMC().getValue() != null) {
                            System.out.println("  车辆名称: " + cert.getCLMC().getValue());
                        }
                        
                        // 车辆品牌
                        if (cert.getCLPP() != null && cert.getCLPP().getValue() != null) {
                            System.out.println("  车辆品牌: " + cert.getCLPP().getValue());
                        }
                        
                        // 车辆型号
                        if (cert.getCLXH() != null && cert.getCLXH().getValue() != null) {
                            System.out.println("  车辆型号: " + cert.getCLXH().getValue());
                        }
                        
                        // 车身颜色
                        if (cert.getCSYS() != null && cert.getCSYS().getValue() != null) {
                            System.out.println("  车身颜色: " + cert.getCSYS().getValue());
                        }
                        
                        System.out.println();
                    }
                } else {
                    System.out.println("未找到匹配的证书记录");
                }
                
            } else {
                System.out.println("✗ 查询返回null");
            }
            
        } catch (Exception e) {
            System.out.println("✗ 查询调用失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 性能测试
     */
    private static void performanceTest() {
        System.out.println("\n--- 测试4: 性能测试 ---");
        
        int testCount = 3;
        long totalTime = 0;
        int successCount = 0;
        
        for (int i = 1; i <= testCount; i++) {
            System.out.println("第 " + i + " 次测试:");
            long startTime = System.currentTimeMillis();
            
            try {
                CertificateRequestVIP port = WSClient.getPort();
                String result = port.helloWorld();
                
                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;
                totalTime += duration;
                successCount++;
                
                System.out.println("  ✓ 成功 - 耗时: " + duration + "ms - 响应: " + result);
                
            } catch (Exception e) {
                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;
                System.out.println("  ✗ 失败 - 耗时: " + duration + "ms - 错误: " + e.getMessage());
            }
        }
        
        System.out.println("\n性能测试结果:");
        System.out.println("  总测试次数: " + testCount);
        System.out.println("  成功次数: " + successCount);
        System.out.println("  成功率: " + (successCount * 100.0 / testCount) + "%");
        if (successCount > 0) {
            System.out.println("  平均响应时间: " + (totalTime / successCount) + "ms");
        }
    }
    
    /**
     * 对比测试 - WSClient vs DynamicWSClient
     */
    private static void comparisonTest() {
        System.out.println("\n--- 测试5: 对比测试 WSClient vs DynamicWSClient ---");
        
        // 测试WSClient
        System.out.println("1. WSClient测试:");
        try {
            long startTime = System.currentTimeMillis();
            CertificateRequestVIP port = WSClient.getPort();
            List<CertificateInfo> results = port.queryCertificateSingle(
                "HX231008U001", "D#$>sy38", "", "LGHY5J2G2RC053149");
            long endTime = System.currentTimeMillis();
            
            System.out.println("  ✓ WSClient成功 - 耗时: " + (endTime - startTime) + "ms");
            System.out.println("  返回类型: List<CertificateInfo>");
            System.out.println("  记录数: " + (results != null ? results.size() : 0));
            
            if (results != null && !results.isEmpty()) {
                CertificateInfo first = results.get(0);
                System.out.println("  第一条记录:");
                if (first.getWZHGZBH() != null && first.getWZHGZBH().getValue() != null) {
                    System.out.println("    证书编号: " + first.getWZHGZBH().getValue());
                }
                if (first.getCLSBDH() != null && first.getCLSBDH().getValue() != null) {
                    System.out.println("    车辆识别代号: " + first.getCLSBDH().getValue());
                }
            }
            
        } catch (Exception e) {
            System.out.println("  ✗ WSClient失败: " + e.getMessage());
        }
        
        // 测试DynamicWSClient
        System.out.println("\n2. DynamicWSClient测试:");
        try {
            long startTime = System.currentTimeMillis();
            String result = DynamicWSClient.testQuery(
                "HX231008U001", "D#$>sy38", "", "LGHY5J2G2RC053149");
            long endTime = System.currentTimeMillis();
            
            System.out.println("  ✓ DynamicWSClient成功 - 耗时: " + (endTime - startTime) + "ms");
            System.out.println("  返回类型: String");
            System.out.println("  响应长度: " + (result != null ? result.length() : 0) + " 字符");
            
            if (result != null && result.length() > 100) {
                System.out.println("  响应预览: " + result.substring(0, 100) + "...");
            }
            
        } catch (Exception e) {
            System.out.println("  ✗ DynamicWSClient失败: " + e.getMessage());
        }
        
        System.out.println("\n结论: WSClient直接返回结构化对象，更方便使用！");
        System.out.println("推荐在业务代码中使用WSClient而不是DynamicWSClient");
    }
}
