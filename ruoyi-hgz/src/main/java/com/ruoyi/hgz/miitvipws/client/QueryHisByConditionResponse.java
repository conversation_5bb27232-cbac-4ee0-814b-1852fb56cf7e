
package com.ruoyi.hgz.miitvipws.client;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>QueryHisByConditionResponse complex type鐨� Java 绫汇��
 * 
 * <p>浠ヤ笅妯″紡鐗囨鎸囧畾鍖呭惈鍦ㄦ绫讳腑鐨勯鏈熷唴瀹广��
 * 
 * <pre>
 * &lt;complexType name="QueryHisByConditionResponse">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="QueryHisByConditionResult" type="{http://www.vidc.info/certificate/operation/}CertificateInfo" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "QueryHisByConditionResponse", propOrder = {
    "queryHisByConditionResult"
})
public class QueryHisByConditionResponse {

    @XmlElement(name = "QueryHisByConditionResult", namespace = "http://www.vidc.info/certificate/operation/")
    protected List<CertificateInfo> queryHisByConditionResult;

    /**
     * Gets the value of the queryHisByConditionResult property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the queryHisByConditionResult property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getQueryHisByConditionResult().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link CertificateInfo }
     * 
     * 
     */
    public List<CertificateInfo> getQueryHisByConditionResult() {
        if (queryHisByConditionResult == null) {
            queryHisByConditionResult = new ArrayList<CertificateInfo>();
        }
        return this.queryHisByConditionResult;
    }

}
