
package com.ruoyi.hgz.miitvipws.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>UploadUpdate_EntEXResponse complex type鐨� Java 绫汇��
 * 
 * <p>浠ヤ笅妯″紡鐗囨鎸囧畾鍖呭惈鍦ㄦ绫讳腑鐨勯鏈熷唴瀹广��
 * 
 * <pre>
 * &lt;complexType name="UploadUpdate_EntEXResponse">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="UploadUpdate_EntEXResult" type="{http://www.vidc.info/certificate/operation/}OperateResult" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "UploadUpdate_EntEXResponse", propOrder = {
    "uploadUpdateEntEXResult"
})
public class UploadUpdateEntEXResponse {

    @XmlElement(name = "UploadUpdate_EntEXResult", namespace = "http://www.vidc.info/certificate/operation/")
    protected OperateResult uploadUpdateEntEXResult;

    /**
     * 鑾峰彇uploadUpdateEntEXResult灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link OperateResult }
     *     
     */
    public OperateResult getUploadUpdateEntEXResult() {
        return uploadUpdateEntEXResult;
    }

    /**
     * 璁剧疆uploadUpdateEntEXResult灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link OperateResult }
     *     
     */
    public void setUploadUpdateEntEXResult(OperateResult value) {
        this.uploadUpdateEntEXResult = value;
    }

}
