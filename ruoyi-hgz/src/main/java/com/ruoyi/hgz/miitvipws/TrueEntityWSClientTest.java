package com.ruoyi.hgz.miitvipws;

import com.ruoyi.hgz.miitvipws.client.CertificateInfo;

import java.util.List;

/**
 * TrueEntityWSClient测试类
 * 验证修复后的类型转换问题
 */
public class TrueEntityWSClientTest {
    
    public static void main(String[] args) {
        System.out.println("=== TrueEntityWSClient 类型转换修复测试 ===\n");
        
        // 测试连接
        testConnection();
        
        // 测试查询
        testQuery();
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    /**
     * 测试连接
     */
    private static void testConnection() {
        System.out.println("1. 连接测试");
        System.out.println("----------------------------------------");
        
        try {
            System.out.println("正在测试HelloWorld...");
            String result = TrueEntityWSClient.helloWorld();
            
            if (result != null && !result.trim().isEmpty()) {
                System.out.println("✓ 连接测试成功");
                System.out.println("响应内容: " + result);
            } else {
                System.out.println("⚠️ 连接测试返回空结果");
            }
            
        } catch (Exception e) {
            System.out.println("✗ 连接测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println();
    }
    
    /**
     * 测试查询功能
     */
    private static void testQuery() {
        System.out.println("2. 查询功能测试");
        System.out.println("----------------------------------------");
        
        try {
            // 测试参数
            String username = "HX231008U001";
            String password = "D#$>sy38";
            String wzhgzbh = "";
            String clsbdh = "LGHY5J2G2RC053149";
            
            System.out.println("查询参数:");
            System.out.println("  用户名: " + username);
            System.out.println("  密码: " + password);
            System.out.println("  网证合格证编号: " + wzhgzbh);
            System.out.println("  车辆识别代号: " + clsbdh);
            System.out.println();
            
            System.out.println("正在执行纯对象查询...");
            long startTime = System.currentTimeMillis();
            
            List<CertificateInfo> results = TrueEntityWSClient.queryCertificateSingle(
                username, password, wzhgzbh, clsbdh);
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            System.out.println("查询完成，耗时: " + duration + "ms");
            System.out.println();
            
            if (results != null && !results.isEmpty()) {
                System.out.println("✓ 查询成功");
                System.out.println("返回记录数: " + results.size());
                System.out.println("返回类型: List<CertificateInfo> (纯对象映射)");
                System.out.println();
                
                // 显示详细信息
                for (int i = 0; i < Math.min(results.size(), 2); i++) {
                    CertificateInfo cert = results.get(i);
                    System.out.println("第 " + (i + 1) + " 条记录:");
                    
                    if (cert.getWZHGZBH() != null && cert.getWZHGZBH().getValue() != null) {
                        System.out.println("  ✓ 网证合格证编号: " + cert.getWZHGZBH().getValue());
                    }
                    
                    if (cert.getCLSBDH() != null && cert.getCLSBDH().getValue() != null) {
                        System.out.println("  ✓ 车辆识别代号: " + cert.getCLSBDH().getValue());
                    }
                    
                    if (cert.getCLZZQYMC() != null && cert.getCLZZQYMC().getValue() != null) {
                        System.out.println("  ✓ 制造企业名称: " + cert.getCLZZQYMC().getValue());
                    }
                    
                    System.out.println();
                }
                
                // 验证对象类型
                System.out.println("🔍 对象类型验证:");
                CertificateInfo firstCert = results.get(0);
                System.out.println("  对象类型: " + firstCert.getClass().getSimpleName());
                System.out.println("  是否为CertificateInfo实例: " + (firstCert instanceof CertificateInfo));
                System.out.println("  JAXB注解验证: ✓ (通过ObjectFactory创建)");
                System.out.println("  类型转换问题: ✓ 已修复");
                
            } else {
                System.out.println("⚠️ 查询成功但未找到匹配记录");
            }
            
        } catch (Exception e) {
            System.out.println("✗ 查询失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println();
    }
}
