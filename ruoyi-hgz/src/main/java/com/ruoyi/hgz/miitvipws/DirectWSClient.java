package com.ruoyi.hgz.miitvipws;

import com.ruoyi.hgz.miitvipws.client.CertificateInfo;
import com.ruoyi.hgz.miitvipws.client.ObjectFactory;

import javax.net.ssl.*;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBElement;
import javax.xml.bind.Unmarshaller;
import javax.xml.datatype.XMLGregorianCalendar;
import javax.xml.namespace.QName;
import javax.xml.soap.*;
import javax.xml.ws.Dispatch;
import javax.xml.ws.Service;
import javax.xml.ws.soap.SOAPBinding;
import java.io.StringReader;
import java.net.URL;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 直接WebService客户端
 * 使用SOAP消息直接调用，避免WSDL依赖问题
 * 直接返回CertificateInfo对象，无需手动解析
 */
public class DirectWSClient {
    
    private static final String SERVICE_URL = "https://hgz.miit.gov.cn/enterprise/services/CertificateRequestVIPService";
    private static final String NAMESPACE_URI = "http://www.vidc.info/certificate/operation/";
    private static final String SERVICE_NAME = "CertificateRequestVIP";
    private static final String PORT_NAME = "CertificateRequestVIPServiceImplPort";
    
    private static volatile Dispatch<SOAPMessage> dispatch;
    private static volatile JAXBContext jaxbContext;
    private static volatile boolean sslInitialized = false;
    private static final ObjectFactory factory = new ObjectFactory();
    
    /**
     * 初始化SSL配置
     */
    private static synchronized void initSSL() throws Exception {
        if (sslInitialized) {
            return;
        }
        
        try {
            TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() { return null; }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                }
            };
            
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
            HttpsURLConnection.setDefaultHostnameVerifier((hostname, session) -> true);
            
            sslInitialized = true;
            System.out.println("DirectWSClient SSL配置完成");
        } catch (Exception e) {
            System.err.println("DirectWSClient SSL配置失败: " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * 初始化JAXB上下文
     */
    private static synchronized void initJAXBContext() throws Exception {
        if (jaxbContext == null) {
            jaxbContext = JAXBContext.newInstance(CertificateInfo.class, ObjectFactory.class);
        }
    }
    
    /**
     * 获取Dispatch客户端
     */
    private static synchronized Dispatch<SOAPMessage> getDispatch() throws Exception {
        if (dispatch == null) {
            initSSL();
            
            QName serviceQName = new QName(NAMESPACE_URI, SERVICE_NAME);
            QName portQName = new QName(NAMESPACE_URI, PORT_NAME);
            
            Service service = Service.create(serviceQName);
            service.addPort(portQName, SOAPBinding.SOAP11HTTP_BINDING, SERVICE_URL);
            
            dispatch = service.createDispatch(portQName, SOAPMessage.class, Service.Mode.MESSAGE);
            
            System.out.println("DirectWSClient Dispatch客户端创建成功");
        }
        return dispatch;
    }
    
    /**
     * HelloWorld方法
     */
    public static String helloWorld() {
        try {
            SOAPMessage request = createHelloWorldMessage();
            Dispatch<SOAPMessage> client = getDispatch();
            SOAPMessage response = client.invoke(request);
            
            return extractTextFromSOAPMessage(response);
            
        } catch (Exception e) {
            throw new RuntimeException("HelloWorld调用失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 查询单个证书信息 - 直接返回CertificateInfo对象
     */
    public static List<CertificateInfo> queryCertificateSingle(String username, String password, 
                                                               String wzhgzbh, String clsbdh) {
        try {
            System.out.println("DirectWSClient 正在查询证书信息...");
            
            SOAPMessage request = createQueryMessage(username, password, wzhgzbh, clsbdh);
            Dispatch<SOAPMessage> client = getDispatch();
            SOAPMessage response = client.invoke(request);
            
            // 解析SOAP响应为CertificateInfo对象
            return parseSOAPResponseToCertificateInfo(response);
            
        } catch (Exception e) {
            throw new RuntimeException("查询证书信息失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建HelloWorld SOAP消息
     */
    private static SOAPMessage createHelloWorldMessage() throws Exception {
        MessageFactory messageFactory = MessageFactory.newInstance();
        SOAPMessage soapMessage = messageFactory.createMessage();
        SOAPPart soapPart = soapMessage.getSOAPPart();
        
        SOAPEnvelope envelope = soapPart.getEnvelope();
        envelope.addNamespaceDeclaration("tns", NAMESPACE_URI);
        
        SOAPBody body = envelope.getBody();
        SOAPElement helloWorldElement = body.addChildElement("HelloWorld", "tns");
        
        soapMessage.saveChanges();
        return soapMessage;
    }
    
    /**
     * 创建查询SOAP消息
     */
    private static SOAPMessage createQueryMessage(String username, String password, 
                                                  String wzhgzbh, String clsbdh) throws Exception {
        MessageFactory messageFactory = MessageFactory.newInstance();
        SOAPMessage soapMessage = messageFactory.createMessage();
        SOAPPart soapPart = soapMessage.getSOAPPart();
        
        SOAPEnvelope envelope = soapPart.getEnvelope();
        envelope.addNamespaceDeclaration("tns", NAMESPACE_URI);
        
        SOAPBody body = envelope.getBody();
        SOAPElement queryElement = body.addChildElement("QueryCertificateSingle", "tns");
        
        // 添加参数
        SOAPElement usernameElement = queryElement.addChildElement("username", "tns");
        usernameElement.addTextNode(username);
        
        SOAPElement passwordElement = queryElement.addChildElement("password", "tns");
        passwordElement.addTextNode(password);
        
        SOAPElement wzhgzbhElement = queryElement.addChildElement("wzhgzbh", "tns");
        wzhgzbhElement.addTextNode(wzhgzbh != null ? wzhgzbh : "");
        
        SOAPElement clsbdhElement = queryElement.addChildElement("clsbdh", "tns");
        clsbdhElement.addTextNode(clsbdh);
        
        soapMessage.saveChanges();
        return soapMessage;
    }
    
    /**
     * 从SOAP消息中提取文本内容
     */
    private static String extractTextFromSOAPMessage(SOAPMessage message) throws Exception {
        SOAPBody body = message.getSOAPBody();
        return extractTextFromSOAPBody(body);
    }
    
    /**
     * 从SOAP Body中提取文本内容
     */
    private static String extractTextFromSOAPBody(SOAPBody body) throws Exception {
        StringBuilder result = new StringBuilder();
        extractTextFromNode(body, result);
        return result.toString().trim();
    }
    
    /**
     * 解析SOAP响应为CertificateInfo对象列表
     */
    private static List<CertificateInfo> parseSOAPResponseToCertificateInfo(SOAPMessage response) throws Exception {
        List<CertificateInfo> result = new ArrayList<>();
        
        try {
            // 首先尝试直接解析SOAP响应
            SOAPBody responseBody = response.getSOAPBody();
            String responseText = extractTextFromSOAPBody(responseBody);
            
            System.out.println("DirectWSClient 收到响应，长度: " + responseText.length());
            
            // 使用智能解析方法
            CertificateInfo cert = parseResponseText(responseText);
            if (cert != null) {
                result.add(cert);
                System.out.println("DirectWSClient 成功解析出 1 条证书信息");
            } else {
                System.out.println("DirectWSClient 未能解析出有效的证书信息");
            }
            
        } catch (Exception e) {
            System.err.println("DirectWSClient 解析SOAP响应失败: " + e.getMessage());
            throw e;
        }
        
        return result;
    }
    
    /**
     * 智能解析响应文本为CertificateInfo对象
     */
    private static CertificateInfo parseResponseText(String responseText) {
        if (responseText == null || responseText.trim().isEmpty()) {
            return null;
        }

        try {
            // 打印响应内容用于调试
            System.out.println("DirectWSClient 响应内容预览: " +
                (responseText.length() > 200 ? responseText.substring(0, 200) + "..." : responseText));

            // 直接使用文本解析（最可靠的方案）
            CertificateInfo textResult = tryParseText(responseText);
            if (textResult != null) {
                System.out.println("DirectWSClient 使用文本解析成功");
                return textResult;
            }

            // 尝试XML格式解析（如果JAXB可用）
            try {
                initJAXBContext();
                CertificateInfo xmlResult = tryParseXML(responseText);
                if (xmlResult != null) {
                    System.out.println("DirectWSClient 使用XML解析成功");
                    return xmlResult;
                }
            } catch (Exception jaxbError) {
                System.out.println("DirectWSClient JAXB不可用，跳过XML解析: " + jaxbError.getMessage());
            }

            // 尝试JSON格式解析
            CertificateInfo jsonResult = tryParseJSON(responseText);
            if (jsonResult != null) {
                System.out.println("DirectWSClient 使用JSON解析成功");
                return jsonResult;
            }

            System.out.println("DirectWSClient 所有解析方法都失败");
            return null;

        } catch (Exception e) {
            System.err.println("DirectWSClient 解析响应失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 尝试XML格式解析
     */
    private static CertificateInfo tryParseXML(String responseText) {
        try {
            // 查找XML格式的CertificateInfo
            Pattern xmlPattern = Pattern.compile("<CertificateInfo[^>]*>.*?</CertificateInfo>", Pattern.DOTALL);
            Matcher xmlMatcher = xmlPattern.matcher(responseText);

            if (xmlMatcher.find()) {
                String xmlContent = xmlMatcher.group();
                Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();
                Object result = unmarshaller.unmarshal(new StringReader(xmlContent));

                if (result instanceof CertificateInfo) {
                    return (CertificateInfo) result;
                } else if (result instanceof JAXBElement) {
                    Object value = ((JAXBElement<?>) result).getValue();
                    if (value instanceof CertificateInfo) {
                        return (CertificateInfo) value;
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("DirectWSClient XML解析失败: " + e.getMessage());
        }
        return null;
    }

    /**
     * 尝试JSON格式解析
     */
    private static CertificateInfo tryParseJSON(String responseText) {
        try {
            // 简单的JSON解析（可以根据需要扩展）
            if (responseText.contains("{") && responseText.contains("}")) {
                CertificateInfo cert = factory.createCertificateInfo();

                // 提取JSON中的字段
                String wzhgzbh = extractJSONValue(responseText, "WZHGZBH");
                String clsbdh = extractJSONValue(responseText, "CLSBDH");
                String clmc = extractJSONValue(responseText, "CLMC");

                if (wzhgzbh != null) {
                    cert.setWZHGZBH(factory.createCertificateInfoWZHGZBH(wzhgzbh));
                }
                if (clsbdh != null) {
                    cert.setCLSBDH(factory.createCertificateInfoCLSBDH(clsbdh));
                }
                if (clmc != null) {
                    cert.setCLMC(factory.createCertificateInfoCLMC(clmc));
                }

                return cert;
            }
        } catch (Exception e) {
            System.out.println("DirectWSClient JSON解析失败: " + e.getMessage());
        }
        return null;
    }

    /**
     * 尝试文本格式解析
     */
    private static CertificateInfo tryParseText(String responseText) {
        try {
            CertificateInfo cert = factory.createCertificateInfo();
            boolean hasData = false;

            Object res[] = responseText.split("\\s+");
            if (res.length>=3){
                cert.setWZHGZBH(factory.createCertificateInfoWZHGZBH(String.valueOf(res[62])));
                cert.setCLSBDH(factory.createCertificateInfoCLSBDH(String.valueOf(res[1])));
                cert.setCLZZQYMC(factory.createCertificateInfoCLZZQYMC(String.valueOf(res[2])));
                cert.setCLMC(factory.createCertificateInfoCLMC(String.valueOf(res[4])));
                cert.setCLXH(factory.createCertificateInfoCLXH(String.valueOf(res[9])));
                cert.setCLZZRQ(factory.createCertificateInfoCLZZRQ((XMLGregorianCalendar) res[30]));
                cert.setFZRQ(factory.createCertificateInfoCLZZRQ((XMLGregorianCalendar) res[35]));
                cert.setPC(factory.createCertificateInfoPC(String.valueOf(res[57])));
                cert.setCREATETIME(factory.createCertificateInfoCREATETIME((XMLGregorianCalendar) res[41]));
                cert.setVEHICLESTATUS(factory.createCertificateInfoVEHICLESTATUS((String) res[42]));
                hasData = true;
            }


        } catch (Exception e) {
            System.out.println("DirectWSClient 文本解析失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 从JSON文本中提取指定字段的值
     */
    private static String extractJSONValue(String jsonText, String fieldName) {
        try {
            Pattern pattern = Pattern.compile("\"" + fieldName + "\"\\s*:\\s*\"([^\"]+)\"", Pattern.CASE_INSENSITIVE);
            Matcher matcher = pattern.matcher(jsonText);
            if (matcher.find()) {
                return matcher.group(1);
            }
        } catch (Exception e) {
            // 忽略解析错误
        }
        return null;
    }

    /**
     * 递归提取节点中的文本内容
     */
    private static void extractTextFromNode(org.w3c.dom.Node node, StringBuilder result) {
        if (node.getNodeType() == org.w3c.dom.Node.TEXT_NODE) {
            String text = node.getNodeValue().trim();
            if (!text.isEmpty()) {
                if (result.length() > 0) {
                    result.append(" ");
                }
                result.append(text);
            }
        } else if (node.getNodeType() == org.w3c.dom.Node.ELEMENT_NODE) {
            org.w3c.dom.NodeList children = node.getChildNodes();
            for (int i = 0; i < children.getLength(); i++) {
                extractTextFromNode(children.item(i), result);
            }
        }
    }

    /**
     * 重置连接
     */
    public static synchronized void resetConnection() {
        dispatch = null;
        System.out.println("DirectWSClient 连接已重置");
    }

    /**
     * 验证连接
     */
    public static boolean validateConnection() {
        try {
            String result = helloWorld();
            return result != null && result.contains("Hello");
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取服务信息
     */
    public static String getServiceInfo() {
        return "DirectWSClient - 直接SOAP消息WebService客户端\n" +
               "使用SOAP消息直接调用，避免WSDL依赖问题\n" +
               "支持多种响应格式解析：XML、JSON、文本\n" +
               "直接返回CertificateInfo对象，无需手动解析";
    }
}
