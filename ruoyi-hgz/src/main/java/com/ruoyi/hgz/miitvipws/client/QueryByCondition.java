
package com.ruoyi.hgz.miitvipws.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>QueryByCondition complex type鐨� Java 绫汇��
 * 
 * <p>浠ヤ笅妯″紡鐗囨鎸囧畾鍖呭惈鍦ㄦ绫讳腑鐨勯鏈熷唴瀹广��
 * 
 * <pre>
 * &lt;complexType name="QueryByCondition">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="username" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="password" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="wzhgzbh" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="clsbdh" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="clxh" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="status" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="startTime" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="endTime" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pagesite" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="pageSize" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "QueryByCondition", propOrder = {
    "username",
    "password",
    "wzhgzbh",
    "clsbdh",
    "clxh",
    "status",
    "startTime",
    "endTime",
    "pagesite",
    "pageSize"
})
public class QueryByCondition {

    @XmlElement(namespace = "http://www.vidc.info/certificate/operation/")
    protected String username;
    @XmlElement(namespace = "http://www.vidc.info/certificate/operation/")
    protected String password;
    @XmlElement(namespace = "http://www.vidc.info/certificate/operation/")
    protected String wzhgzbh;
    @XmlElement(namespace = "http://www.vidc.info/certificate/operation/")
    protected String clsbdh;
    @XmlElement(namespace = "http://www.vidc.info/certificate/operation/")
    protected String clxh;
    @XmlElement(namespace = "http://www.vidc.info/certificate/operation/")
    protected String status;
    @XmlElement(namespace = "http://www.vidc.info/certificate/operation/")
    protected String startTime;
    @XmlElement(namespace = "http://www.vidc.info/certificate/operation/")
    protected String endTime;
    @XmlElement(namespace = "http://www.vidc.info/certificate/operation/")
    protected int pagesite;
    @XmlElement(namespace = "http://www.vidc.info/certificate/operation/")
    protected int pageSize;

    /**
     * 鑾峰彇username灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUsername() {
        return username;
    }

    /**
     * 璁剧疆username灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUsername(String value) {
        this.username = value;
    }

    /**
     * 鑾峰彇password灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPassword() {
        return password;
    }

    /**
     * 璁剧疆password灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPassword(String value) {
        this.password = value;
    }

    /**
     * 鑾峰彇wzhgzbh灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWzhgzbh() {
        return wzhgzbh;
    }

    /**
     * 璁剧疆wzhgzbh灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWzhgzbh(String value) {
        this.wzhgzbh = value;
    }

    /**
     * 鑾峰彇clsbdh灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getClsbdh() {
        return clsbdh;
    }

    /**
     * 璁剧疆clsbdh灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setClsbdh(String value) {
        this.clsbdh = value;
    }

    /**
     * 鑾峰彇clxh灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getClxh() {
        return clxh;
    }

    /**
     * 璁剧疆clxh灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setClxh(String value) {
        this.clxh = value;
    }

    /**
     * 鑾峰彇status灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStatus() {
        return status;
    }

    /**
     * 璁剧疆status灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStatus(String value) {
        this.status = value;
    }

    /**
     * 鑾峰彇startTime灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStartTime() {
        return startTime;
    }

    /**
     * 璁剧疆startTime灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStartTime(String value) {
        this.startTime = value;
    }

    /**
     * 鑾峰彇endTime灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEndTime() {
        return endTime;
    }

    /**
     * 璁剧疆endTime灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEndTime(String value) {
        this.endTime = value;
    }

    /**
     * 鑾峰彇pagesite灞炴�х殑鍊笺��
     * 
     */
    public int getPagesite() {
        return pagesite;
    }

    /**
     * 璁剧疆pagesite灞炴�х殑鍊笺��
     * 
     */
    public void setPagesite(int value) {
        this.pagesite = value;
    }

    /**
     * 鑾峰彇pageSize灞炴�х殑鍊笺��
     * 
     */
    public int getPageSize() {
        return pageSize;
    }

    /**
     * 璁剧疆pageSize灞炴�х殑鍊笺��
     * 
     */
    public void setPageSize(int value) {
        this.pageSize = value;
    }

}
