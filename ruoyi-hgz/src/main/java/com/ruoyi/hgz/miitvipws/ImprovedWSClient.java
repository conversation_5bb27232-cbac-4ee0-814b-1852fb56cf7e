package com.ruoyi.hgz.miitvipws;

import com.ruoyi.hgz.miitvipws.client.CertificateInfo;
import com.ruoyi.hgz.miitvipws.client.CertificateRequestVIP;
import com.ruoyi.hgz.miitvipws.client.CertificateRequestVIP_Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.*;
import javax.xml.namespace.QName;
import javax.xml.ws.BindingProvider;
import javax.xml.ws.Service;
import javax.xml.ws.WebServiceException;
import java.net.URL;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.List;
import java.util.Map;

/**
 * 改进的WebService客户端
 * 提供更好的错误处理和多种连接方式
 */
public class ImprovedWSClient {
    
    private static final Logger logger = LoggerFactory.getLogger(ImprovedWSClient.class);
    
    // 配置常量
    private static final String SERVICE_URL = "https://hgz.miit.gov.cn/enterprise/services/CertificateRequestVIPService";
    private static final String NAMESPACE_URI = "http://www.vidc.info/certificate/operation/";
    private static final String SERVICE_NAME = "CertificateRequestVIP";
    private static final String PORT_NAME = "CertificateRequestVIPServiceImplPort";
    
    // 超时配置
    private static final int CONNECT_TIMEOUT = 30000; // 30秒
    private static final int REQUEST_TIMEOUT = 60000; // 60秒
    
    private static volatile CertificateRequestVIP port;
    private static volatile boolean sslInitialized = false;
    
    /**
     * 初始化SSL配置
     */
    private static synchronized void initSSL() throws Exception {
        if (sslInitialized) {
            return;
        }
        
        try {
            // 创建信任所有证书的TrustManager
            TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                }
            };
            
            // 安装信任所有证书的TrustManager
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
            
            // 创建主机名验证器
            HostnameVerifier allHostsValid = (hostname, session) -> true;
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);
            
            sslInitialized = true;
            logger.info("SSL配置初始化成功");
        } catch (Exception e) {
            logger.error("SSL配置初始化失败", e);
            throw e;
        }
    }
    
    /**
     * 获取WebService端口 - 使用WSDL方式
     */
    public static synchronized CertificateRequestVIP getPortWithWSDL() throws Exception {
        if (port == null) {
            try {
                initSSL();
                
                // 尝试使用本地WSDL文件
                URL wsdlUrl = ImprovedWSClient.class.getResource("/com/ruoyi/hgz/miitvipws/CertificateRequestVIPService.wsdl");
                if (wsdlUrl == null) {
                    logger.warn("本地WSDL文件未找到，尝试使用远程WSDL");
                    wsdlUrl = new URL(SERVICE_URL + "?wsdl");
                }
                
                logger.info("使用WSDL URL: {}", wsdlUrl);
                
                QName qName = new QName(NAMESPACE_URI, SERVICE_NAME);
                CertificateRequestVIP_Service service = new CertificateRequestVIP_Service(wsdlUrl, qName);
                port = service.getCertificateRequestVIPServiceImplPort();
                
                configurePort(port);
                
                logger.info("WebService客户端创建成功 (WSDL方式)");
            } catch (Exception e) {
                logger.error("创建WebService客户端失败 (WSDL方式)", e);
                throw new RuntimeException("创建WebService客户端失败: " + e.getMessage(), e);
            }
        }
        return port;
    }
    
    /**
     * 获取WebService端口 - 不使用WSDL方式
     */
    public static CertificateRequestVIP getPortWithoutWSDL() throws Exception {
        try {
            initSSL();
            
            logger.info("使用动态创建方式连接WebService");
            
            // 创建服务和端口，不使用WSDL
            QName serviceQName = new QName(NAMESPACE_URI, SERVICE_NAME);
            QName portQName = new QName(NAMESPACE_URI, PORT_NAME);
            
            Service service = Service.create(serviceQName);
            service.addPort(portQName, 
                            javax.xml.ws.soap.SOAPBinding.SOAP11HTTP_BINDING, 
                            SERVICE_URL);
            
            CertificateRequestVIP dynamicPort = service.getPort(portQName, CertificateRequestVIP.class);
            configurePort(dynamicPort);
            
            logger.info("WebService客户端创建成功 (动态方式)");
            return dynamicPort;
        } catch (Exception e) {
            logger.error("创建WebService客户端失败 (动态方式)", e);
            throw new RuntimeException("创建WebService客户端失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 配置端口属性
     */
    private static void configurePort(CertificateRequestVIP port) {
        BindingProvider bp = (BindingProvider) port;
        Map<String, Object> requestContext = bp.getRequestContext();
        
        // 设置端点地址
        requestContext.put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, SERVICE_URL);
        
        // 设置超时
        requestContext.put("com.sun.xml.ws.connect.timeout", CONNECT_TIMEOUT);
        requestContext.put("com.sun.xml.ws.request.timeout", REQUEST_TIMEOUT);
        
        // 设置用户代理
        requestContext.put("com.sun.xml.ws.transport.http.client.HttpTransportPipe.userAgent", 
                          "Java WebService Client");
        
        logger.info("端口配置完成 - 连接超时: {}ms, 请求超时: {}ms", CONNECT_TIMEOUT, REQUEST_TIMEOUT);
    }
    
    /**
     * 获取WebService端口 - 智能选择方式
     */
    public static CertificateRequestVIP getPort() {
        try {
            // 首先尝试使用WSDL方式
            return getPortWithWSDL();
        } catch (Exception e) {
            logger.warn("WSDL方式连接失败，尝试动态方式: {}", e.getMessage());
            try {
                return getPortWithoutWSDL();
            } catch (Exception e2) {
                logger.error("所有连接方式都失败", e2);
                throw new RuntimeException("无法创建WebService连接: " + e2.getMessage(), e2);
            }
        }
    }
    
    /**
     * 测试连接
     */
    public static String testConnection() {
        try {
            CertificateRequestVIP testPort = getPort();
            String result = testPort.helloWorld();
            logger.info("连接测试成功: {}", result);
            return result;
        } catch (Exception e) {
            logger.error("连接测试失败", e);
            return "连接测试失败: " + e.getMessage();
        }
    }
    
    /**
     * 查询证书信息
     */
    public static List<CertificateInfo> queryCertificateSingle(String username, String password, 
                                                               String wzhgzbh, String clsbdh) {
        try {
            CertificateRequestVIP queryPort = getPort();
            List<CertificateInfo> result = queryPort.queryCertificateSingle(username, password, wzhgzbh, clsbdh);
            logger.info("查询成功，返回 {} 条记录", result != null ? result.size() : 0);
            return result;
        } catch (Exception e) {
            logger.error("查询失败", e);
            throw new RuntimeException("查询证书信息失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 重置连接
     */
    public static synchronized void resetConnection() {
        port = null;
        logger.info("WebService连接已重置");
    }
}
