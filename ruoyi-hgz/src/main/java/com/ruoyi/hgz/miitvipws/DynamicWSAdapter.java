package com.ruoyi.hgz.miitvipws;

import com.ruoyi.hgz.miitvipws.client.CertificateInfo;
import com.ruoyi.hgz.miitvipws.client.ObjectFactory;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBElement;
import javax.xml.bind.Unmarshaller;
import javax.xml.namespace.QName;
import javax.xml.soap.*;
import javax.xml.ws.Dispatch;
import javax.xml.ws.Service;
import javax.xml.ws.soap.SOAPBinding;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 动态WebService适配器
 * 将DynamicWSClient的SOAP响应转换为CertificateInfo对象
 */
public class DynamicWSAdapter {
    
    private static final String SERVICE_URL = "https://hgz.miit.gov.cn/enterprise/services/CertificateRequestVIPService";
    private static final String NAMESPACE_URI = "http://www.vidc.info/certificate/operation/";
    private static final String SERVICE_NAME = "CertificateRequestVIP";
    private static final String PORT_NAME = "CertificateRequestVIPServiceImplPort";
    
    private static volatile Dispatch<SOAPMessage> dispatch;
    
    /**
     * 初始化Dispatch客户端
     */
    private static synchronized Dispatch<SOAPMessage> getDispatch() throws Exception {
        if (dispatch == null) {
            // 初始化SSL
            DynamicWSClient.class.getDeclaredMethod("initSSL").setAccessible(true);
            
            QName serviceQName = new QName(NAMESPACE_URI, SERVICE_NAME);
            QName portQName = new QName(NAMESPACE_URI, PORT_NAME);
            
            Service service = Service.create(serviceQName);
            service.addPort(portQName, SOAPBinding.SOAP11HTTP_BINDING, SERVICE_URL);
            
            dispatch = service.createDispatch(portQName, SOAPMessage.class, Service.Mode.MESSAGE);
        }
        return dispatch;
    }
    
    /**
     * 查询单个证书信息
     */
    public static List<CertificateInfo> queryCertificateSingle(String username, String password, 
                                                               String wzhgzbh, String clsbdh) {
        try {
            // 调用DynamicWSClient获取原始响应
            String rawResponse = DynamicWSClient.testQuery(username, password, wzhgzbh, clsbdh);
            
            // 解析响应并转换为CertificateInfo对象
            return parseQueryResponse(rawResponse);
            
        } catch (Exception e) {
            throw new RuntimeException("查询证书信息失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 解析查询响应
     */
    private static List<CertificateInfo> parseQueryResponse(String rawResponse) {
        List<CertificateInfo> result = new ArrayList<>();

        if (rawResponse == null || rawResponse.trim().isEmpty()) {
            return result;
        }

        try {
            // 简单的文本解析方式
            // 根据实际响应格式调整解析逻辑
            String[] parts = rawResponse.split("\\s+");

            if (parts.length > 0) {
                CertificateInfo cert = new CertificateInfo();
                ObjectFactory factory = new ObjectFactory();

                // 解析证书编号（第一个字段）
                if (parts.length > 0 && !parts[0].isEmpty()) {
                    cert.setWZHGZBH(factory.createCertificateInfoWZHGZBH(parts[0]));
                }

                // 解析车辆识别代号（第二个字段）
                if (parts.length > 1 && !parts[1].isEmpty()) {
                    cert.setCLSBDH(factory.createCertificateInfoCLSBDH(parts[1]));
                }

                // 解析制造商（第三个字段）
                if (parts.length > 2 && !parts[2].isEmpty()) {
                    cert.setCLZZQYMC(factory.createCertificateInfoCLZZQYMC(parts[2]));
                }

                // 解析车辆类型（第四个字段）
                if (parts.length > 3 && !parts[3].isEmpty()) {
                    cert.setCLLX(factory.createCertificateInfoCLLX(parts[3]));
                }

                // 解析车辆名称（第五个字段）
                if (parts.length > 4 && !parts[4].isEmpty()) {
                    cert.setCLMC(factory.createCertificateInfoCLMC(parts[4]));
                }

                // 解析品牌（第六个字段）
                if (parts.length > 5 && !parts[5].isEmpty()) {
                    cert.setCLPP(factory.createCertificateInfoCLPP(parts[5]));
                }

                // 解析车辆型号（第七个字段）
                if (parts.length > 6 && !parts[6].isEmpty()) {
                    cert.setCLXH(factory.createCertificateInfoCLXH(parts[6]));
                }

                // 解析颜色（第八个字段）
                if (parts.length > 7 && !parts[7].isEmpty()) {
                    cert.setYS(factory.createCertificateInfoYS(parts[7]));
                }

                result.add(cert);
            }

        } catch (Exception e) {
            System.err.println("解析响应失败: " + e.getMessage());
            // 如果解析失败，返回空列表而不是抛出异常
        }

        return result;
    }
    
    /**
     * 测试连接
     */
    public static String testConnection() {
        try {
            return DynamicWSClient.testHelloWorld();
        } catch (Exception e) {
            throw new RuntimeException("连接测试失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 重置连接
     */
    public static void resetConnection() {
        dispatch = null;
        DynamicWSClient.resetConnection();
    }
    
    /**
     * 高级解析方法 - 使用正则表达式提取关键信息
     */
    private static List<CertificateInfo> parseQueryResponseAdvanced(String rawResponse) {
        List<CertificateInfo> result = new ArrayList<>();
        
        if (rawResponse == null || rawResponse.trim().isEmpty()) {
            return result;
        }
        
        try {
            ObjectFactory factory = new ObjectFactory();
            
            // 使用正则表达式提取证书编号
            Pattern certPattern = Pattern.compile("HIDC\\d+");
            Matcher certMatcher = certPattern.matcher(rawResponse);
            
            // 使用正则表达式提取VIN码
            Pattern vinPattern = Pattern.compile("[A-Z0-9]{17}");
            Matcher vinMatcher = vinPattern.matcher(rawResponse);
            
            if (certMatcher.find() || vinMatcher.find()) {
                CertificateInfo cert = new CertificateInfo();
                
                if (certMatcher.find()) {
                    cert.setWZHGZBH(factory.createCertificateInfoWZHGZBH(certMatcher.group()));
                }
                
                if (vinMatcher.find()) {
                    cert.setCLSBDH(factory.createCertificateInfoCLSBDH(vinMatcher.group()));
                }
                
                // 提取制造商信息
                Pattern companyPattern = Pattern.compile("([\\u4e00-\\u9fa5]+(?:汽车|制造|有限公司|集团)[\\u4e00-\\u9fa5]*)");
                Matcher companyMatcher = companyPattern.matcher(rawResponse);
                if (companyMatcher.find()) {
                    cert.setCLZZQYMC(factory.createCertificateInfoCLZZQYMC(companyMatcher.group()));
                }
                
                result.add(cert);
            }
            
        } catch (Exception e) {
            System.err.println("高级解析失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 创建查询SOAP消息
     */
    private static SOAPMessage createQueryMessage(String username, String password, String wzhgzbh, String clsbdh) throws Exception {
        MessageFactory messageFactory = MessageFactory.newInstance();
        SOAPMessage soapMessage = messageFactory.createMessage();
        SOAPPart soapPart = soapMessage.getSOAPPart();
        
        SOAPEnvelope envelope = soapPart.getEnvelope();
        envelope.addNamespaceDeclaration("tns", NAMESPACE_URI);
        
        SOAPHeader header = envelope.getHeader();
        SOAPBody body = envelope.getBody();
        
        // 创建QueryCertificateSingle元素
        SOAPElement queryElement = body.addChildElement("QueryCertificateSingle", "tns");
        
        // 添加参数
        SOAPElement usernameElement = queryElement.addChildElement("username", "tns");
        usernameElement.addTextNode(username);
        
        SOAPElement passwordElement = queryElement.addChildElement("password", "tns");
        passwordElement.addTextNode(password);
        
        SOAPElement wzhgzbhElement = queryElement.addChildElement("wzhgzbh", "tns");
        wzhgzbhElement.addTextNode(wzhgzbh);
        
        SOAPElement clsbdhElement = queryElement.addChildElement("clsbdh", "tns");
        clsbdhElement.addTextNode(clsbdh);
        
        soapMessage.saveChanges();
        return soapMessage;
    }
    
    /**
     * 直接SOAP调用方法（备用）
     */
    public static List<CertificateInfo> queryCertificateSingleDirect(String username, String password, 
                                                                     String wzhgzbh, String clsbdh) {
        try {
            Dispatch<SOAPMessage> client = getDispatch();
            SOAPMessage request = createQueryMessage(username, password, wzhgzbh, clsbdh);
            
            SOAPMessage response = client.invoke(request);
            
            // 提取响应文本
            SOAPBody responseBody = response.getSOAPBody();
            String responseText = extractTextContent(responseBody);
            
            return parseQueryResponse(responseText);
            
        } catch (Exception e) {
            throw new RuntimeException("直接SOAP查询失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 提取SOAP响应中的文本内容
     */
    private static String extractTextContent(SOAPBody body) throws Exception {
        StringBuilder result = new StringBuilder();
        extractTextFromNode(body, result);
        return result.toString();
    }
    
    /**
     * 递归提取节点中的文本内容
     */
    private static void extractTextFromNode(org.w3c.dom.Node node, StringBuilder result) {
        if (node.getNodeType() == org.w3c.dom.Node.TEXT_NODE) {
            String text = node.getNodeValue().trim();
            if (!text.isEmpty()) {
                if (result.length() > 0) {
                    result.append(" ");
                }
                result.append(text);
            }
        } else if (node.getNodeType() == org.w3c.dom.Node.ELEMENT_NODE) {
            org.w3c.dom.NodeList children = node.getChildNodes();
            for (int i = 0; i < children.getLength(); i++) {
                extractTextFromNode(children.item(i), result);
            }
        }
    }
}
