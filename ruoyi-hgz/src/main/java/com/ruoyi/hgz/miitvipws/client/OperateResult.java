
package com.ruoyi.hgz.miitvipws.client;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>OperateResult complex type鐨� Java 绫汇��
 * 
 * <p>浠ヤ笅妯″紡鐗囨鎸囧畾鍖呭惈鍦ㄦ绫讳腑鐨勯鏈熷唴瀹广��
 * 
 * <pre>
 * &lt;complexType name="OperateResult">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ResultCode" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="ResultDetail" minOccurs="0">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="NameValuePair" type="{http://www.vidc.info/certificate/operation/}NameValuePair" maxOccurs="unbounded" minOccurs="0"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OperateResult", propOrder = {
    "resultCode",
    "resultDetail"
})
public class OperateResult {

    @XmlElement(name = "ResultCode", namespace = "http://www.vidc.info/certificate/operation/")
    protected int resultCode;
    @XmlElement(name = "ResultDetail", namespace = "http://www.vidc.info/certificate/operation/")
    protected OperateResult.ResultDetail resultDetail;

    /**
     * 鑾峰彇resultCode灞炴�х殑鍊笺��
     * 
     */
    public int getResultCode() {
        return resultCode;
    }

    /**
     * 璁剧疆resultCode灞炴�х殑鍊笺��
     * 
     */
    public void setResultCode(int value) {
        this.resultCode = value;
    }

    /**
     * 鑾峰彇resultDetail灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link OperateResult.ResultDetail }
     *     
     */
    public OperateResult.ResultDetail getResultDetail() {
        return resultDetail;
    }

    /**
     * 璁剧疆resultDetail灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link OperateResult.ResultDetail }
     *     
     */
    public void setResultDetail(OperateResult.ResultDetail value) {
        this.resultDetail = value;
    }


    /**
     * <p>anonymous complex type鐨� Java 绫汇��
     * 
     * <p>浠ヤ笅妯″紡鐗囨鎸囧畾鍖呭惈鍦ㄦ绫讳腑鐨勯鏈熷唴瀹广��
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="NameValuePair" type="{http://www.vidc.info/certificate/operation/}NameValuePair" maxOccurs="unbounded" minOccurs="0"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "nameValuePair"
    })
    public static class ResultDetail {

        @XmlElement(name = "NameValuePair", namespace = "http://www.vidc.info/certificate/operation/")
        protected List<NameValuePair> nameValuePair;

        /**
         * Gets the value of the nameValuePair property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the nameValuePair property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getNameValuePair().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link NameValuePair }
         * 
         * 
         */
        public List<NameValuePair> getNameValuePair() {
            if (nameValuePair == null) {
                nameValuePair = new ArrayList<NameValuePair>();
            }
            return this.nameValuePair;
        }

    }

}
