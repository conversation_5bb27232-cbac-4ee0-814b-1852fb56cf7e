package com.ruoyi.hgz.miitvipws;

import com.ruoyi.hgz.miitvipws.client.CertificateInfo;

import java.util.List;

/**
 * 所有WebService客户端对比测试
 * 验证各个客户端的功能和性能
 */
public class AllClientsTest {
    
    public static void main(String[] args) {
        System.out.println("=== WebService客户端全面测试 ===");
        
        // 测试参数
        String username = "HX231008U001";
        String password = "D#$>sy38";
        String wzhgzbh = "";
        String clsbdh = "LGHY5J2G2RC053149";
        
        System.out.println("测试参数:");
        System.out.println("  用户名: " + username);
        System.out.println("  密码: " + password);
        System.out.println("  网证合格证编号: " + (wzhgzbh.isEmpty() ? "(空)" : wzhgzbh));
        System.out.println("  车辆识别代号: " + clsbdh);
        System.out.println();
        
        // 测试各个客户端
        testWsClient(username, password, wzhgzbh, clsbdh);
        testSmartWSClient(username, password, wzhgzbh, clsbdh);
        testDirectWSClient(username, password, wzhgzbh, clsbdh);
        testFixedWSClient(username, password, wzhgzbh, clsbdh);
        testProductionWSClient(username, password, wzhgzbh, clsbdh);
        
        // 总结
        printSummary();
    }
    
    /**
     * 测试WsClient（统一接口）
     */
    private static void testWsClient(String username, String password, String wzhgzbh, String clsbdh) {
        System.out.println("1. 测试WsClient（统一接口）");
        System.out.println("----------------------------------------");
        
        try {
            // 连接测试
            long startTime = System.currentTimeMillis();
            String helloResult = WsClient.testConnection();
            long helloTime = System.currentTimeMillis() - startTime;
            
            System.out.println("✓ 连接测试成功 - 耗时: " + helloTime + "ms");
            System.out.println("  响应: " + helloResult);
            
            // 查询测试
            startTime = System.currentTimeMillis();
            List<CertificateInfo> results = WsClient.queryCertificateSingle(username, password, wzhgzbh, clsbdh);
            long queryTime = System.currentTimeMillis() - startTime;
            
            System.out.println("✓ 查询测试成功 - 耗时: " + queryTime + "ms");
            System.out.println("  返回记录数: " + results.size());
            
            if (!results.isEmpty()) {
                CertificateInfo cert = results.get(0);
                if (cert.getCLSBDH() != null && cert.getCLSBDH().getValue() != null) {
                    System.out.println("  车辆识别代号: " + cert.getCLSBDH().getValue());
                }
            }
            
        } catch (Exception e) {
            System.out.println("✗ 测试失败: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 测试SmartWSClient（智能客户端）
     */
    private static void testSmartWSClient(String username, String password, String wzhgzbh, String clsbdh) {
        System.out.println("2. 测试SmartWSClient（智能客户端）");
        System.out.println("----------------------------------------");
        
        try {
            // 连接测试
            long startTime = System.currentTimeMillis();
            String helloResult = SmartWSClient.helloWorld();
            long helloTime = System.currentTimeMillis() - startTime;
            
            System.out.println("✓ 连接测试成功 - 耗时: " + helloTime + "ms");
            
            // 查询测试
            startTime = System.currentTimeMillis();
            List<CertificateInfo> results = SmartWSClient.queryCertificateSingle(username, password, wzhgzbh, clsbdh);
            long queryTime = System.currentTimeMillis() - startTime;
            
            System.out.println("✓ 查询测试成功 - 耗时: " + queryTime + "ms");
            System.out.println("  返回记录数: " + results.size());
            System.out.println("  特点: 多策略自动切换，智能降级");
            
        } catch (Exception e) {
            System.out.println("✗ 测试失败: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 测试DirectWSClient（直接客户端）
     */
    private static void testDirectWSClient(String username, String password, String wzhgzbh, String clsbdh) {
        System.out.println("3. 测试DirectWSClient（直接客户端）");
        System.out.println("----------------------------------------");
        
        try {
            // 连接测试
            long startTime = System.currentTimeMillis();
            String helloResult = DirectWSClient.helloWorld();
            long helloTime = System.currentTimeMillis() - startTime;
            
            System.out.println("✓ 连接测试成功 - 耗时: " + helloTime + "ms");
            
            // 查询测试
            startTime = System.currentTimeMillis();
            List<CertificateInfo> results = DirectWSClient.queryCertificateSingle(username, password, wzhgzbh, clsbdh);
            long queryTime = System.currentTimeMillis() - startTime;
            
            System.out.println("✓ 查询测试成功 - 耗时: " + queryTime + "ms");
            System.out.println("  返回记录数: " + results.size());
            System.out.println("  特点: 直接SOAP调用，智能文本解析");
            
        } catch (Exception e) {
            System.out.println("✗ 测试失败: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 测试FixedWSClient（修复客户端）
     */
    private static void testFixedWSClient(String username, String password, String wzhgzbh, String clsbdh) {
        System.out.println("4. 测试FixedWSClient（修复客户端）");
        System.out.println("----------------------------------------");
        
        try {
            // 连接测试
            long startTime = System.currentTimeMillis();
            String helloResult = FixedWSClient.helloWorld();
            long helloTime = System.currentTimeMillis() - startTime;
            
            System.out.println("✓ 连接测试成功 - 耗时: " + helloTime + "ms");
            
            // 查询测试
            startTime = System.currentTimeMillis();
            List<CertificateInfo> results = FixedWSClient.queryCertificateSingle(username, password, wzhgzbh, clsbdh);
            long queryTime = System.currentTimeMillis() - startTime;
            
            System.out.println("✓ 查询测试成功 - 耗时: " + queryTime + "ms");
            System.out.println("  返回记录数: " + results.size());
            System.out.println("  特点: 基于DirectWSClient的封装");
            
        } catch (Exception e) {
            System.out.println("✗ 测试失败: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 测试ProductionWSClient（生产客户端）
     */
    private static void testProductionWSClient(String username, String password, String wzhgzbh, String clsbdh) {
        System.out.println("5. 测试ProductionWSClient（生产客户端）");
        System.out.println("----------------------------------------");
        
        try {
            // 连接测试
            long startTime = System.currentTimeMillis();
            String helloResult = ProductionWSClient.testConnection();
            long helloTime = System.currentTimeMillis() - startTime;
            
            System.out.println("✓ 连接测试成功 - 耗时: " + helloTime + "ms");
            
            // 查询测试
            startTime = System.currentTimeMillis();
            List<CertificateInfo> results = ProductionWSClient.queryCertificateSingle(username, password, wzhgzbh, clsbdh);
            long queryTime = System.currentTimeMillis() - startTime;
            
            System.out.println("✓ 查询测试成功 - 耗时: " + queryTime + "ms");
            System.out.println("  返回记录数: " + results.size());
            System.out.println("  特点: 标准JAX-WS实现，生产环境推荐");
            
        } catch (Exception e) {
            System.out.println("✗ 测试失败: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 打印测试总结
     */
    private static void printSummary() {
        System.out.println("=== 测试总结 ===");
        System.out.println();
        System.out.println("🏆 推荐使用顺序:");
        System.out.println("1. 🥇 WsClient - 统一接口，最佳选择");
        System.out.println("   ✓ 封装了SmartWSClient的所有功能");
        System.out.println("   ✓ 提供简洁统一的API");
        System.out.println("   ✓ 自动处理各种异常情况");
        System.out.println();
        System.out.println("2. 🥈 SmartWSClient - 智能客户端");
        System.out.println("   ✓ 多策略自动切换");
        System.out.println("   ✓ 智能降级机制");
        System.out.println("   ✓ 最高的成功率");
        System.out.println();
        System.out.println("3. 🥉 DirectWSClient - 直接客户端");
        System.out.println("   ✓ 直接SOAP调用");
        System.out.println("   ✓ 智能文本解析");
        System.out.println("   ✓ 无WSDL依赖");
        System.out.println();
        System.out.println("4. FixedWSClient - 修复客户端");
        System.out.println("   ✓ 基于DirectWSClient的简单封装");
        System.out.println();
        System.out.println("5. ProductionWSClient - 生产客户端");
        System.out.println("   ✓ 标准JAX-WS实现");
        System.out.println("   ✓ 适合生产环境");
        System.out.println();
        System.out.println("💡 建议:");
        System.out.println("- 业务代码统一使用 WsClient");
        System.out.println("- WsClient 内部使用 SmartWSClient 提供最佳兼容性");
        System.out.println("- 所有客户端都支持 UTF-8 编码");
        System.out.println("- 已解决 WSDL 依赖问题");
    }
}
