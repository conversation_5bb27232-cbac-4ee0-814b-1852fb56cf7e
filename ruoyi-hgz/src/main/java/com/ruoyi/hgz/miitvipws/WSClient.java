package com.ruoyi.hgz.miitvipws;

import com.ruoyi.hgz.miitvipws.client.CertificateRequestVIP;
import com.ruoyi.hgz.miitvipws.client.CertificateRequestVIP_Service;

import javax.net.ssl.*;
import javax.xml.namespace.QName;
import javax.xml.ws.BindingProvider;
import javax.xml.ws.Service;
import java.net.URL;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;

public class WSClient {
    private static final String SERVICE_URL = "https://hgz.miit.gov.cn/enterprise/services/CertificateRequestVIPService";
    private static final String NAMESPACE_URI = "http://www.vidc.info/certificate/operation/";
    private static final String SERVICE_NAME = "CertificateRequestVIP";

    private static CertificateRequestVIP port;

    // 移除静态初始化块，改为在getPort方法中初始化SSL
    public static synchronized CertificateRequestVIP getPort() {
        if (port == null) {
            try {
                // 初始化SSL
                initSSL();
                
                // 使用ManualWSClient的方式创建端口，避免WSDL加载问题
                QName serviceQName = new QName(NAMESPACE_URI, SERVICE_NAME);
                QName portQName = new QName(NAMESPACE_URI, "CertificateRequestVIPServiceImplPort");
                
                Service service = Service.create(serviceQName);
                service.addPort(portQName, 
                                javax.xml.ws.soap.SOAPBinding.SOAP11HTTP_BINDING, 
                                SERVICE_URL);
                
                port = service.getPort(portQName, CertificateRequestVIP.class);
                
                // 设置端点地址
                BindingProvider bp = (BindingProvider) port;
                bp.getRequestContext().put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, SERVICE_URL);
                
                // 设置超时
                bp.getRequestContext().put("com.sun.xml.ws.connect.timeout", 30000);
                bp.getRequestContext().put("com.sun.xml.ws.request.timeout", 60000);
            } catch (Exception e) {
                throw new RuntimeException("创建WebService客户端失败", e);
            }
        }
        return port;
    }

    static void initSSL() throws Exception {
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() { return null; }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                }
        };
        SSLContext sc = SSLContext.getInstance("SSL");
        sc.init(null, trustAllCerts, new SecureRandom());
        HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
        HttpsURLConnection.setDefaultHostnameVerifier((hostname, session) -> true);
    }
}