
package com.ruoyi.hgz.miitvipws.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>NameValuePair complex type鐨� Java 绫汇��
 * 
 * <p>浠ヤ笅妯″紡鐗囨鎸囧畾鍖呭惈鍦ㄦ绫讳腑鐨勯鏈熷唴瀹广��
 * 
 * <pre>
 * &lt;complexType name="NameValuePair">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Value" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "NameValuePair", propOrder = {
    "name",
    "value"
})
public class NameValuePair {

    @XmlElement(name = "Name", namespace = "http://www.vidc.info/certificate/operation/")
    protected String name;
    @XmlElement(name = "Value", namespace = "http://www.vidc.info/certificate/operation/")
    protected String value;

    /**
     * 鑾峰彇name灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getName() {
        return name;
    }

    /**
     * 璁剧疆name灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setName(String value) {
        this.name = value;
    }

    /**
     * 鑾峰彇value灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getValue() {
        return value;
    }

    /**
     * 璁剧疆value灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setValue(String value) {
        this.value = value;
    }

}
