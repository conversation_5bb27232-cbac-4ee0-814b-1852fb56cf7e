package com.ruoyi.hgz.miitvipws;

import com.ruoyi.hgz.miitvipws.client.CertificateInfo;
import com.ruoyi.hgz.miitvipws.client.CertificateRequestVIP;

import javax.net.ssl.*;
import javax.xml.namespace.QName;
import javax.xml.ws.BindingProvider;
import javax.xml.ws.Service;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.List;
import java.util.Map;

/**
 * 生产环境WebService客户端
 * 使用标准JAX-WS方式，适用于生产环境
 * 动态创建服务，避免WSDL文件依赖
 */
public class ProductionWSClient {
    
    private static final String SERVICE_URL = "https://hgz.miit.gov.cn/enterprise/services/CertificateRequestVIPService";
    private static final String NAMESPACE_URI = "http://www.vidc.info/certificate/operation/";
    private static final String SERVICE_NAME = "CertificateRequestVIP";
    private static final String PORT_NAME = "CertificateRequestVIPServiceImplPort";
    
    // 超时设置
    private static final int CONNECT_TIMEOUT = 30000; // 30秒
    private static final int REQUEST_TIMEOUT = 60000; // 60秒
    
    private static volatile CertificateRequestVIP port;
    private static volatile boolean sslInitialized = false;
    
    /**
     * 初始化SSL配置
     */
    private static synchronized void initSSL() throws Exception {
        if (sslInitialized) {
            return;
        }
        
        try {
            // 创建信任所有证书的TrustManager
            TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() { return null; }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                }
            };
            
            // 安装信任所有证书的TrustManager
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
            
            // 创建信任所有主机名的HostnameVerifier
            HttpsURLConnection.setDefaultHostnameVerifier(new HostnameVerifier() {
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            });
            
            sslInitialized = true;
            System.out.println("ProductionWSClient SSL配置完成");
            
        } catch (Exception e) {
            System.err.println("ProductionWSClient SSL配置失败: " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * 获取WebService端口
     * 使用动态方式创建，避免WSDL依赖问题
     */
    public static synchronized CertificateRequestVIP getPort() {
        if (port == null) {
            try {
                initSSL();
                
                // 使用动态方式创建服务，避免WSDL加载问题
                QName serviceQName = new QName(NAMESPACE_URI, SERVICE_NAME);
                QName portQName = new QName(NAMESPACE_URI, PORT_NAME);
                
                Service service = Service.create(serviceQName);
                service.addPort(portQName, 
                                javax.xml.ws.soap.SOAPBinding.SOAP11HTTP_BINDING, 
                                SERVICE_URL);
                
                port = service.getPort(portQName, CertificateRequestVIP.class);
                
                // 配置端口属性
                configurePort(port);
                
            } catch (Exception e) {
                throw new RuntimeException("创建WebService客户端失败: " + e.getMessage(), e);
            }
        }
        return port;
    }
    
    /**
     * 配置端口属性
     */
    private static void configurePort(CertificateRequestVIP port) {
        BindingProvider bp = (BindingProvider) port;
        Map<String, Object> requestContext = bp.getRequestContext();
        
        // 设置端点地址
        requestContext.put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, SERVICE_URL);
        
        // 设置超时
        requestContext.put("com.sun.xml.ws.connect.timeout", CONNECT_TIMEOUT);
        requestContext.put("com.sun.xml.ws.request.timeout", REQUEST_TIMEOUT);
        
        // 设置用户代理
        requestContext.put("com.sun.xml.ws.transport.http.client.HttpTransportPipe.userAgent", 
                          "RuoYi-HGZ WebService Client v1.0");
    }
    
    /**
     * 测试连接
     * @return 服务器响应消息
     */
    public static String testConnection() {
        try {
            CertificateRequestVIP client = getPort();
            return client.helloWorld();
        } catch (Exception e) {
            throw new RuntimeException("连接测试失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 查询单个证书信息
     * 
     * @param username 用户名
     * @param password 密码
     * @param wzhgzbh 网证合格证编号（可为空）
     * @param clsbdh 车辆识别代号
     * @return 证书信息列表
     */
    public static List<CertificateInfo> queryCertificateSingle(String username, String password, 
                                                               String wzhgzbh, String clsbdh) {
        try {
            CertificateRequestVIP client = getPort();
            return client.queryCertificateSingle(username, password, wzhgzbh, clsbdh);
        } catch (Exception e) {
            throw new RuntimeException("查询证书信息失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 按条件查询证书信息
     * 
     * @param username 用户名
     * @param password 密码
     * @param wzhgzbh 网证合格证编号
     * @param clsbdh 车辆识别代号
     * @param clxh 车辆型号
     * @param status 状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pagesite 页码
     * @param pageSize 页大小
     * @return 证书信息列表
     */
    public static List<CertificateInfo> queryByCondition(String username, String password,
                                                         String wzhgzbh, String clsbdh, String clxh,
                                                         String status, String startTime, String endTime,
                                                         int pagesite, int pageSize) {
        try {
            CertificateRequestVIP client = getPort();
            return client.queryByCondition(username, password, wzhgzbh, clsbdh, clxh, 
                                         status, startTime, endTime, pagesite, pageSize);
        } catch (Exception e) {
            throw new RuntimeException("按条件查询证书信息失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 重置连接
     * 清除缓存的端口实例，强制重新连接
     */
    public static synchronized void resetConnection() {
        port = null;
        System.out.println("ProductionWSClient 连接已重置");
    }
    
    /**
     * 验证连接是否正常
     * @return true表示连接正常，false表示连接异常
     */
    public static boolean validateConnection() {
        try {
            String result = testConnection();
            return result != null && result.contains("Hello");
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取服务信息
     * @return 服务信息字符串
     */
    public static String getServiceInfo() {
        return "ProductionWSClient - 生产环境WebService客户端\n" +
               "使用标准JAX-WS方式，适用于生产环境\n" +
               "动态创建服务，避免WSDL文件依赖\n" +
               "支持完整的WebService接口功能";
    }
    
    /**
     * 获取连接状态
     * @return true表示已连接，false表示未连接
     */
    public static boolean isConnected() {
        return port != null && validateConnection();
    }
}
