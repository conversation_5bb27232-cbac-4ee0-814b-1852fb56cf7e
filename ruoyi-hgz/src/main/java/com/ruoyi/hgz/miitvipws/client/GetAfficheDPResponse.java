
package com.ruoyi.hgz.miitvipws.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>GetAfficheDPResponse complex type鐨� Java 绫汇��
 * 
 * <p>浠ヤ笅妯″紡鐗囨鎸囧畾鍖呭惈鍦ㄦ绫讳腑鐨勯鏈熷唴瀹广��
 * 
 * <pre>
 * &lt;complexType name="GetAfficheDPResponse">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="GetAfficheDPResult" type="{http://www.w3.org/2001/XMLSchema}base64Binary" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "GetAfficheDPResponse", propOrder = {
    "getAfficheDPResult"
})
public class GetAfficheDPResponse {

    @XmlElement(name = "GetAfficheDPResult", namespace = "http://www.vidc.info/certificate/operation/")
    protected byte[] getAfficheDPResult;

    /**
     * 鑾峰彇getAfficheDPResult灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     byte[]
     */
    public byte[] getGetAfficheDPResult() {
        return getAfficheDPResult;
    }

    /**
     * 璁剧疆getAfficheDPResult灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     byte[]
     */
    public void setGetAfficheDPResult(byte[] value) {
        this.getAfficheDPResult = value;
    }

}
