package com.ruoyi.hgz.miitvipws;

import com.ruoyi.hgz.miitvipws.client.CertificateInfo;

import java.util.Date;
import java.util.List;

/**
 * 最终测试类 - 验证所有问题都已解决
 */
public class FinalTest {
    
    public static void main(String[] args) {
        System.out.println("=== 最终WebService修复验证测试 ===");
        System.out.println("测试时间: " + new Date());
        
        // 测试1: ProductionWSClient
        testProductionWSClient();
        
        // 测试2: 修复后的WSClient
        testFixedWSClient();
        
        // 测试3: ManualWSClient
        testManualWSClient();
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    /**
     * 测试ProductionWSClient（推荐使用）
     */
    private static void testProductionWSClient() {
        System.out.println("\n--- 测试1: ProductionWSClient ---");
        
        try {
            System.out.println("正在测试连接...");
            String result = ProductionWSClient.testConnection();
            System.out.println("✓ 连接测试成功: " + result);
            
            System.out.println("正在测试查询...");
            List<CertificateInfo> certs = ProductionWSClient.queryCertificateSingle(
                "HX231008U001", "D#$>sy38", "", "LGHY5J2G2RC053149");
            
            if (certs != null && !certs.isEmpty()) {
                System.out.println("✓ 查询成功，返回 " + certs.size() + " 条记录");
                CertificateInfo first = certs.get(0);
                if (first.getWZHGZBH() != null && first.getWZHGZBH().getValue() != null) {
                    System.out.println("  证书编号: " + first.getWZHGZBH().getValue());
                }
                if (first.getCLSBDH() != null && first.getCLSBDH().getValue() != null) {
                    System.out.println("  车辆识别代号: " + first.getCLSBDH().getValue());
                }
            } else {
                System.out.println("✓ 查询成功，但未找到匹配记录");
            }
            
        } catch (Exception e) {
            System.out.println("✗ ProductionWSClient测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试修复后的WSClient
     */
    private static void testFixedWSClient() {
        System.out.println("\n--- 测试2: 修复后的WSClient ---");
        
        try {
            System.out.println("正在创建连接...");
            com.ruoyi.hgz.miitvipws.client.CertificateRequestVIP port = WSClient.getPort();
            System.out.println("✓ 连接创建成功");
            
            System.out.println("正在测试HelloWorld...");
            String result = port.helloWorld();
            System.out.println("✓ HelloWorld成功: " + result);
            
            System.out.println("正在测试查询...");
            List<CertificateInfo> certs = port.queryCertificateSingle(
                "HX231008U001", "D#$>sy38", "", "LGHY5J2G2RC053149");
            
            if (certs != null && !certs.isEmpty()) {
                System.out.println("✓ 查询成功，返回 " + certs.size() + " 条记录");
            } else {
                System.out.println("✓ 查询成功，但未找到匹配记录");
            }
            
        } catch (Exception e) {
            System.out.println("✗ WSClient测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试ManualWSClient
     */
    private static void testManualWSClient() {
        System.out.println("\n--- 测试3: ManualWSClient ---");
        
        try {
            System.out.println("正在测试HelloWorld...");
            String result = ManualWSClient.testHelloWorld();
            System.out.println("✓ HelloWorld成功: " + result);
            
            System.out.println("正在测试查询...");
            List<CertificateInfo> certs = ManualWSClient.queryCertificateSingle(
                "HX231008U001", "D#$>sy38", "", "LGHY5J2G2RC053149");
            
            if (certs != null && !certs.isEmpty()) {
                System.out.println("✓ 查询成功，返回 " + certs.size() + " 条记录");
            } else {
                System.out.println("✓ 查询成功，但未找到匹配记录");
            }
            
        } catch (Exception e) {
            System.out.println("✗ ManualWSClient测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 性能对比测试
     */
    public static void performanceComparison() {
        System.out.println("\n--- 性能对比测试 ---");
        
        int testCount = 3;
        
        // 测试ProductionWSClient
        System.out.println("ProductionWSClient性能测试:");
        long totalTime1 = 0;
        int successCount1 = 0;
        
        for (int i = 1; i <= testCount; i++) {
            long startTime = System.currentTimeMillis();
            try {
                ProductionWSClient.testConnection();
                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;
                totalTime1 += duration;
                successCount1++;
                System.out.println("  第" + i + "次: " + duration + "ms");
            } catch (Exception e) {
                System.out.println("  第" + i + "次: 失败 - " + e.getMessage());
            }
        }
        
        if (successCount1 > 0) {
            System.out.println("  平均响应时间: " + (totalTime1 / successCount1) + "ms");
        }
        
        // 测试WSClient
        System.out.println("\nWSClient性能测试:");
        long totalTime2 = 0;
        int successCount2 = 0;
        
        for (int i = 1; i <= testCount; i++) {
            long startTime = System.currentTimeMillis();
            try {
                WSClient.getPort().helloWorld();
                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;
                totalTime2 += duration;
                successCount2++;
                System.out.println("  第" + i + "次: " + duration + "ms");
            } catch (Exception e) {
                System.out.println("  第" + i + "次: 失败 - " + e.getMessage());
            }
        }
        
        if (successCount2 > 0) {
            System.out.println("  平均响应时间: " + (totalTime2 / successCount2) + "ms");
        }
    }
    
    /**
     * 错误恢复测试
     */
    public static void errorRecoveryTest() {
        System.out.println("\n--- 错误恢复测试 ---");
        
        try {
            // 重置连接
            ProductionWSClient.resetConnection();
            System.out.println("✓ 连接重置成功");
            
            // 重新连接
            String result = ProductionWSClient.testConnection();
            System.out.println("✓ 重新连接成功: " + result);
            
        } catch (Exception e) {
            System.out.println("✗ 错误恢复测试失败: " + e.getMessage());
        }
    }
}
