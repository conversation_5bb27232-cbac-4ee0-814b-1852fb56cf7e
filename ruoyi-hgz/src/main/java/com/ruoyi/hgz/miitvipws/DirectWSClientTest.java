package com.ruoyi.hgz.miitvipws;

import com.ruoyi.hgz.miitvipws.client.CertificateInfo;

import java.util.Date;
import java.util.List;

/**
 * DirectWSClient测试类
 * 验证DirectWSClient能否直接返回CertificateInfo实体对象
 */
public class DirectWSClientTest {
    
    public static void main(String[] args) {
        System.out.println("=== DirectWSClient实体对象验证测试 ===");
        System.out.println("测试时间: " + new Date());
        
        // 测试1: HelloWorld方法测试
        testHelloWorld();
        
        // 测试2: 查询方法测试 - 重点验证返回实体对象
        testQueryWithEntityValidation();
        
        // 测试3: 对比所有客户端
        comparisonAllClients();
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    /**
     * 测试HelloWorld方法
     */
    private static void testHelloWorld() {
        System.out.println("\n--- 测试1: HelloWorld方法测试 ---");
        
        try {
            System.out.println("正在调用DirectWSClient.helloWorld()...");
            String result = DirectWSClient.helloWorld();
            
            System.out.println("✓ HelloWorld调用成功");
            System.out.println("返回结果: " + result);
            System.out.println("返回类型: " + result.getClass().getSimpleName());
            
        } catch (Exception e) {
            System.out.println("✗ HelloWorld调用失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试查询方法 - 重点验证返回实体对象
     */
    private static void testQueryWithEntityValidation() {
        System.out.println("\n--- 测试2: 查询方法实体对象验证 ---");
        
        try {
            // 测试参数
            String username = "HX231008U001";
            String password = "D#$>sy38";
            String wzhgzbh = "";
            String clsbdh = "LGHY5J2G2RC053149";
            
            System.out.println("查询参数:");
            System.out.println("  用户名: " + username);
            System.out.println("  密码: " + password);
            System.out.println("  网证合格证编号: " + wzhgzbh);
            System.out.println("  车辆识别代号: " + clsbdh);
            
            System.out.println("\n正在调用DirectWSClient.queryCertificateSingle()...");
            List<CertificateInfo> results = DirectWSClient.queryCertificateSingle(username, password, wzhgzbh, clsbdh);
            
            // 验证返回类型
            System.out.println("✓ 查询调用成功");
            System.out.println("返回类型: " + results.getClass().getSimpleName() + "<" + 
                             (results.isEmpty() ? "Empty" : results.get(0).getClass().getSimpleName()) + ">");
            System.out.println("返回记录数: " + results.size());
            
            if (!results.isEmpty()) {
                System.out.println("\n🎯 实体对象验证:");
                for (int i = 0; i < Math.min(results.size(), 2); i++) {
                    CertificateInfo cert = results.get(i);
                    System.out.println("第 " + (i + 1) + " 条记录 (类型: " + cert.getClass().getSimpleName() + "):");
                    
                    // 验证每个字段都是实体对象的属性
                    if (cert.getWZHGZBH() != null) {
                        System.out.println("  ✓ 网证合格证编号: " + cert.getWZHGZBH().getValue() + 
                                         " (类型: " + cert.getWZHGZBH().getClass().getSimpleName() + ")");
                    }
                    
                    if (cert.getCLSBDH() != null) {
                        System.out.println("  ✓ 车辆识别代号: " + cert.getCLSBDH().getValue() + 
                                         " (类型: " + cert.getCLSBDH().getClass().getSimpleName() + ")");
                    }
                    
                    if (cert.getCLZZQYMC() != null) {
                        System.out.println("  ✓ 制造企业名称: " + cert.getCLZZQYMC().getValue() + 
                                         " (类型: " + cert.getCLZZQYMC().getClass().getSimpleName() + ")");
                    }
                    
                    if (cert.getCLLX() != null) {
                        System.out.println("  ✓ 车辆类型: " + cert.getCLLX().getValue() + 
                                         " (类型: " + cert.getCLLX().getClass().getSimpleName() + ")");
                    }
                    
                    if (cert.getCLMC() != null) {
                        System.out.println("  ✓ 车辆名称: " + cert.getCLMC().getValue() + 
                                         " (类型: " + cert.getCLMC().getClass().getSimpleName() + ")");
                    }
                    
                    if (cert.getCLPP() != null) {
                        System.out.println("  ✓ 车辆品牌: " + cert.getCLPP().getValue() + 
                                         " (类型: " + cert.getCLPP().getClass().getSimpleName() + ")");
                    }
                    
                    if (cert.getCLXH() != null) {
                        System.out.println("  ✓ 车辆型号: " + cert.getCLXH().getValue() + 
                                         " (类型: " + cert.getCLXH().getClass().getSimpleName() + ")");
                    }
                    
                    if (cert.getCSYS() != null) {
                        System.out.println("  ✓ 车身颜色: " + cert.getCSYS().getValue() + 
                                         " (类型: " + cert.getCSYS().getClass().getSimpleName() + ")");
                    }
                    
                    System.out.println();
                }
                
                System.out.println("🎉 验证结果: DirectWSClient成功返回了真正的CertificateInfo实体对象！");
                System.out.println("✅ 无需手动解析字符串");
                System.out.println("✅ 可以直接使用对象的getter方法");
                System.out.println("✅ 类型安全，IDE有代码提示");
                
            } else {
                System.out.println("未找到匹配的证书记录，但返回类型正确");
            }
            
        } catch (Exception e) {
            System.out.println("✗ 查询调用失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 对比所有客户端的返回类型
     */
    private static void comparisonAllClients() {
        System.out.println("\n--- 测试3: 所有客户端对比 ---");
        
        String username = "HX231008U001";
        String password = "D#$>sy38";
        String wzhgzbh = "";
        String clsbdh = "LGHY5J2G2RC053149";
        
        // 1. DirectWSClient
        System.out.println("1. DirectWSClient:");
        try {
            long startTime = System.currentTimeMillis();
            List<CertificateInfo> results = DirectWSClient.queryCertificateSingle(username, password, wzhgzbh, clsbdh);
            long endTime = System.currentTimeMillis();
            
            System.out.println("  ✓ 成功 - 耗时: " + (endTime - startTime) + "ms");
            System.out.println("  返回类型: List<CertificateInfo> (真正的实体对象)");
            System.out.println("  记录数: " + results.size());
            System.out.println("  优势: 直接返回实体，类型安全，无需解析");
            
        } catch (Exception e) {
            System.out.println("  ✗ 失败: " + e.getMessage());
        }
        
        // 2. FixedWSClient (基于DirectWSClient)
        System.out.println("\n2. FixedWSClient (基于DirectWSClient):");
        try {
            long startTime = System.currentTimeMillis();
            List<CertificateInfo> results = FixedWSClient.queryCertificateSingle(username, password, wzhgzbh, clsbdh);
            long endTime = System.currentTimeMillis();
            
            System.out.println("  ✓ 成功 - 耗时: " + (endTime - startTime) + "ms");
            System.out.println("  返回类型: List<CertificateInfo> (真正的实体对象)");
            System.out.println("  记录数: " + results.size());
            System.out.println("  优势: 封装了DirectWSClient，提供统一接口");
            
        } catch (Exception e) {
            System.out.println("  ✗ 失败: " + e.getMessage());
        }
        
        // 3. DynamicWSClient (字符串返回)
        System.out.println("\n3. DynamicWSClient (字符串返回):");
        try {
            long startTime = System.currentTimeMillis();
            String result = DynamicWSClient.testQuery(username, password, wzhgzbh, clsbdh);
            long endTime = System.currentTimeMillis();
            
            System.out.println("  ✓ 成功 - 耗时: " + (endTime - startTime) + "ms");
            System.out.println("  返回类型: String (需要手动解析)");
            System.out.println("  响应长度: " + result.length() + " 字符");
            System.out.println("  缺点: 需要手动解析，容易出错，无类型安全");
            
        } catch (Exception e) {
            System.out.println("  ✗ 失败: " + e.getMessage());
        }
        
        System.out.println("\n🏆 推荐排序:");
        System.out.println("1. 🥇 DirectWSClient - 直接返回实体对象，最佳选择");
        System.out.println("2. 🥈 FixedWSClient - 基于DirectWSClient的封装版本");
        System.out.println("3. 🥉 DynamicWSClient - 返回字符串，需要手动解析");
        System.out.println("4. ❌ 原WSClient - 有WSDL依赖问题，不推荐");
    }
    
    /**
     * 验证业务集成
     */
    public static void businessIntegrationTest() {
        System.out.println("\n--- 业务集成测试 ---");
        
        try {
            System.out.println("模拟HgzToolServiceImpl的使用场景:");
            
            // 模拟业务调用
            List<CertificateInfo> list = FixedWSClient.queryCertificateSingle(
                "HX231008U001", "D#$>sy38", "", "LGHY5J2G2RC053149");
            
            if (list != null && list.size() > 0) {
                CertificateInfo certificateInfo = list.get(0);
                
                System.out.println("✓ 业务调用成功");
                System.out.println("✓ 获取到CertificateInfo对象: " + certificateInfo.getClass().getSimpleName());
                System.out.println("✓ 可以直接传递给HgzConvertUtils.convertToWzHgzInfo()");
                
                // 验证关键字段
                if (certificateInfo.getWZHGZBH() != null) {
                    System.out.println("✓ 证书编号可用: " + certificateInfo.getWZHGZBH().getValue());
                }
                
                System.out.println("✅ 业务集成测试通过！");
            } else {
                System.out.println("⚠️ 未找到数据，但接口调用正常");
            }
            
        } catch (Exception e) {
            System.out.println("✗ 业务集成测试失败: " + e.getMessage());
        }
    }
}
