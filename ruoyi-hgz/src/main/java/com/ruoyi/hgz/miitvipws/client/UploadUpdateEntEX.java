
package com.ruoyi.hgz.miitvipws.client;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>UploadUpdate_EntEX complex type鐨� Java 绫汇��
 * 
 * <p>浠ヤ笅妯″紡鐗囨鎸囧畾鍖呭惈鍦ㄦ绫讳腑鐨勯鏈熷唴瀹广��
 * 
 * <pre>
 * &lt;complexType name="UploadUpdate_EntEX">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="username" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="password" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="certificateInfos" type="{http://www.vidc.info/certificate/operation/}CertificateInfo" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="memo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ukey" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "UploadUpdate_EntEX", propOrder = {
    "username",
    "password",
    "certificateInfos",
    "memo",
    "ukey"
})
public class UploadUpdateEntEX {

    @XmlElement(namespace = "http://www.vidc.info/certificate/operation/")
    protected String username;
    @XmlElement(namespace = "http://www.vidc.info/certificate/operation/")
    protected String password;
    @XmlElement(namespace = "http://www.vidc.info/certificate/operation/")
    protected List<CertificateInfo> certificateInfos;
    @XmlElement(namespace = "http://www.vidc.info/certificate/operation/")
    protected String memo;
    @XmlElement(namespace = "http://www.vidc.info/certificate/operation/")
    protected String ukey;

    /**
     * 鑾峰彇username灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUsername() {
        return username;
    }

    /**
     * 璁剧疆username灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUsername(String value) {
        this.username = value;
    }

    /**
     * 鑾峰彇password灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPassword() {
        return password;
    }

    /**
     * 璁剧疆password灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPassword(String value) {
        this.password = value;
    }

    /**
     * Gets the value of the certificateInfos property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the certificateInfos property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getCertificateInfos().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link CertificateInfo }
     * 
     * 
     */
    public List<CertificateInfo> getCertificateInfos() {
        if (certificateInfos == null) {
            certificateInfos = new ArrayList<CertificateInfo>();
        }
        return this.certificateInfos;
    }

    /**
     * 鑾峰彇memo灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMemo() {
        return memo;
    }

    /**
     * 璁剧疆memo灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMemo(String value) {
        this.memo = value;
    }

    /**
     * 鑾峰彇ukey灞炴�х殑鍊笺��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUkey() {
        return ukey;
    }

    /**
     * 璁剧疆ukey灞炴�х殑鍊笺��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUkey(String value) {
        this.ukey = value;
    }

}
