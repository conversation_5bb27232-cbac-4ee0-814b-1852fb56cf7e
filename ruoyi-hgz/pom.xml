<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ruoyi</artifactId>
        <groupId>com.ruoyi</groupId>
        <version>3.8.8</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ruoyi-hgz</artifactId>

    <description>
        hgz系统模块
    </description>

    <dependencies>

        <!-- 通用工具-->
        <dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-common</artifactId>
        </dependency>
        <!-- jacob -->
        <dependency>
            <groupId>io.github.osobolev</groupId>
            <artifactId>jacob</artifactId>
            <version>1.20</version>
        </dependency>




    </dependencies>

    <build>
        <plugins>
            <!-- WSDL插件 - 可选，SmartWSClient不依赖WSDL生成的类 -->
            <plugin>
                <groupId>org.jvnet.jax-ws-commons</groupId>
                <artifactId>jaxws-maven-plugin</artifactId>
                <version>2.3</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>wsimport</goal>
                        </goals>
                        <configuration>
                            <wsdlDirectory>src/main/resources/wsdl</wsdlDirectory>
                            <wsdlFiles>
                                <wsdlFile>CertificateRequestVIPService.wsdl</wsdlFile>
                            </wsdlFiles>
                            <packageName>com.ruoyi.hgz.miitvipws.client</packageName>
                            <sourceDestDir>${project.build.directory}/generated-sources/wsimport</sourceDestDir>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>